# frozen_string_literal: true

##
# Performance Monitoring Middleware
#
# Tracks request performance, database queries, and memory usage.
# Logs slow requests and provides performance insights.
#
class PerformanceMonitoringMiddleware
  SLOW_REQUEST_THRESHOLD = 1000 # milliseconds
  HIGH_QUERY_COUNT_THRESHOLD = 10
  HIGH_MEMORY_THRESHOLD = 50 # MB

  def initialize(app)
    @app = app
  end

  def call(env)
    request = ActionDispatch::Request.new(env)

    # Skip monitoring for assets and health checks
    return @app.call(env) if skip_monitoring?(request)

    start_time = Time.current
    start_memory = memory_usage
    query_count_before = query_count

    response = @app.call(env)

    end_time = Time.current
    end_memory = memory_usage
    query_count_after = query_count

    # Calculate metrics
    duration_ms = ((end_time - start_time) * 1000).round(2)
    memory_used = end_memory - start_memory
    queries_executed = query_count_after - query_count_before

    # Log performance data
    log_performance_data(request, duration_ms, memory_used, queries_executed)

    # Store metrics for analysis
    store_performance_metrics(request, duration_ms, memory_used, queries_executed)

    response
  rescue => e
    Rails.logger.error "Performance monitoring error: #{e.message}"
    response || [ 500, {}, [ "Internal Server Error" ] ]
  end

  private

  def skip_monitoring?(request)
    # Skip assets, health checks, and other non-critical requests
    request.path.start_with?("/assets") ||
    request.path.start_with?("/health") ||
    request.path.start_with?("/favicon") ||
    request.path.start_with?("/robots.txt")
  end

  def memory_usage
    # Get current memory usage in MB
    if defined?(GC.stat)
      (GC.stat[:heap_live_slots] * 40 / 1024.0 / 1024.0).round(2)
    else
      0.0
    end
  end

  def query_count
    # Get current query count from ActiveRecord
    if defined?(ActiveRecord::Base.connection.query_cache)
      ActiveRecord::Base.connection.query_cache.size
    else
      0
    end
  end

  def log_performance_data(request, duration_ms, memory_used, queries_executed)
    log_level = determine_log_level(duration_ms, memory_used, queries_executed)

    log_message = build_log_message(request, duration_ms, memory_used, queries_executed)

    case log_level
    when :warn
      Rails.logger.warn log_message
    when :error
      Rails.logger.error log_message
    else
      Rails.logger.info log_message
    end
  end

  def determine_log_level(duration_ms, memory_used, queries_executed)
    if duration_ms > SLOW_REQUEST_THRESHOLD * 2 ||
       memory_used > HIGH_MEMORY_THRESHOLD * 2 ||
       queries_executed > HIGH_QUERY_COUNT_THRESHOLD * 2
      :error
    elsif duration_ms > SLOW_REQUEST_THRESHOLD ||
          memory_used > HIGH_MEMORY_THRESHOLD ||
          queries_executed > HIGH_QUERY_COUNT_THRESHOLD
      :warn
    else
      :info
    end
  end

  def build_log_message(request, duration_ms, memory_used, queries_executed)
    controller_action = extract_controller_action(request)

    message = "[PERFORMANCE] #{request.method} #{request.path}"
    message += " (#{controller_action})" if controller_action
    message += " - #{duration_ms}ms"
    message += " | #{queries_executed} queries"
    message += " | #{memory_used}MB memory"

    # Add warnings for performance issues
    warnings = []
    warnings << "SLOW REQUEST" if duration_ms > SLOW_REQUEST_THRESHOLD
    warnings << "HIGH QUERY COUNT" if queries_executed > HIGH_QUERY_COUNT_THRESHOLD
    warnings << "HIGH MEMORY USAGE" if memory_used > HIGH_MEMORY_THRESHOLD

    message += " | #{warnings.join(', ')}" if warnings.any?

    message
  end

  def extract_controller_action(request)
    # Try to extract controller and action from request
    if request.env["action_controller.instance"]
      controller = request.env["action_controller.instance"]
      "#{controller.class.name}##{controller.action_name}"
    elsif request.env["action_dispatch.request.path_parameters"]
      params = request.env["action_dispatch.request.path_parameters"]
      "#{params[:controller]}##{params[:action]}" if params[:controller] && params[:action]
    end
  end

  def store_performance_metrics(request, duration_ms, memory_used, queries_executed)
    # Store metrics in Rails cache for analysis
    return unless should_store_metrics?(duration_ms, queries_executed)

    metric_data = {
      path: request.path,
      method: request.method,
      controller_action: extract_controller_action(request),
      duration_ms: duration_ms,
      memory_used: memory_used,
      queries_executed: queries_executed,
      timestamp: Time.current.to_i,
      user_agent: request.user_agent&.truncate(100),
      ip_address: request.remote_ip
    }

    # Store in cache with TTL
    cache_key = "performance_metrics:#{Time.current.strftime('%Y%m%d%H')}:#{SecureRandom.hex(8)}"
    Rails.cache.write(cache_key, metric_data, expires_in: 24.hours)

    # Also store aggregated hourly metrics
    store_hourly_aggregates(metric_data)
  end

  def should_store_metrics?(duration_ms, queries_executed)
    # Store metrics for slow requests or high query counts
    duration_ms > 500 || queries_executed > 5
  end

  def store_hourly_aggregates(metric_data)
    hour_key = "performance_hourly:#{Time.current.strftime('%Y%m%d%H')}"

    # Get existing hourly data or initialize
    hourly_data = Rails.cache.read(hour_key) || {
      request_count: 0,
      total_duration: 0.0,
      total_queries: 0,
      max_duration: 0.0,
      slow_requests: 0
    }

    # Update aggregates
    hourly_data[:request_count] += 1
    hourly_data[:total_duration] += metric_data[:duration_ms]
    hourly_data[:total_queries] += metric_data[:queries_executed]
    hourly_data[:max_duration] = [ hourly_data[:max_duration], metric_data[:duration_ms] ].max
    hourly_data[:slow_requests] += 1 if metric_data[:duration_ms] > SLOW_REQUEST_THRESHOLD

    # Store updated aggregates
    Rails.cache.write(hour_key, hourly_data, expires_in: 25.hours)
  end

  ##
  # Get performance summary for the last 24 hours
  #
  # @return [Hash] Performance summary
  #
  def self.performance_summary
    current_hour = Time.current.hour
    hours_to_check = 24

    summary = {
      total_requests: 0,
      avg_response_time: 0.0,
      max_response_time: 0.0,
      total_slow_requests: 0,
      avg_queries_per_request: 0.0,
      hourly_breakdown: []
    }

    hours_to_check.times do |i|
      hour_time = Time.current - i.hours
      hour_key = "performance_hourly:#{hour_time.strftime('%Y%m%d%H')}"

      hourly_data = Rails.cache.read(hour_key)
      next unless hourly_data

      summary[:total_requests] += hourly_data[:request_count]
      summary[:total_slow_requests] += hourly_data[:slow_requests]
      summary[:max_response_time] = [ summary[:max_response_time], hourly_data[:max_duration] ].max

      summary[:hourly_breakdown] << {
        hour: hour_time.strftime("%H:00"),
        requests: hourly_data[:request_count],
        avg_duration: hourly_data[:request_count] > 0 ?
          (hourly_data[:total_duration] / hourly_data[:request_count]).round(2) : 0.0,
        slow_requests: hourly_data[:slow_requests]
      }
    end

    # Calculate averages
    if summary[:total_requests] > 0
      total_duration = summary[:hourly_breakdown].sum { |h| h[:avg_duration] * h[:requests] }
      total_queries = summary[:hourly_breakdown].sum { |h| h[:requests] * 5 } # Estimate

      summary[:avg_response_time] = (total_duration / summary[:total_requests]).round(2)
      summary[:avg_queries_per_request] = (total_queries / summary[:total_requests]).round(1)
    end

    summary[:hourly_breakdown].reverse! # Most recent first
    summary
  end

  ##
  # Get slow requests from the last hour
  #
  # @return [Array] Array of slow request data
  #
  def self.recent_slow_requests
    current_hour = Time.current.strftime("%Y%m%d%H")
    pattern = "performance_metrics:#{current_hour}:*"

    slow_requests = []

    # This is a simplified version - in production you'd want to use Redis SCAN
    # or a proper time-series database for better performance
    Rails.cache.instance_variable_get(:@data)&.each do |key, value|
      if key.start_with?("performance_metrics:#{current_hour}:") &&
         value[:duration_ms] > SLOW_REQUEST_THRESHOLD
        slow_requests << value
      end
    end

    slow_requests.sort_by { |req| -req[:duration_ms] }.first(20)
  end
end
