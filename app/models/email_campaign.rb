# frozen_string_literal: true

# == Schema Information
# Table name: email_campaigns
#
#  id           :bigint           not null, primary key
#  campaign_id  :bigint           not null
#  subject_line :string(150)      not null
#  preview_text :string(200)
#  content      :text             not null
#  from_name    :string           not null
#  from_email   :string           not null
#  settings     :jsonb            default({}), not null
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#
# Indexes
#
#  index_email_campaigns_on_campaign_id  (campaign_id) UNIQUE
#  index_email_campaigns_on_from_email   (from_email)
#  index_email_campaigns_on_settings     (settings) USING gin
#
# Foreign Keys
#
#  fk_rails_...  (campaign_id => campaigns.id)
#

class EmailCampaign < ApplicationRecord
  # Associations
  belongs_to :campaign, required: true

  # Validations
  validates :subject_line, presence: true, length: { maximum: 150 }
  validates :content, presence: true
  validates :from_name, presence: true
  validates :from_email, presence: true, format: {
    with: URI::MailTo::EMAIL_REGEXP,
    message: "must be a valid email address"
  }
  validates :preview_text, length: { maximum: 200 }, allow_blank: true

  # Scopes
  scope :ready_to_send, -> {
    joins(:campaign)
      .where(campaigns: { status: "active" })
      .where.not(subject_line: [ nil, "" ])
      .where.not(content: [ nil, "" ])
      .where.not(from_email: [ nil, "" ])
  }

  # Delegate tenant access through campaign
  def tenant
    campaign.tenant
  end

  # Instance methods
  def ready_to_send?
    valid? &&
    campaign&.active? &&
    subject_line.present? &&
    content.present? &&
    from_email.present? &&
    valid_email_format?
  end

  def preview_snippet(length = 100)
    return "" if content.blank?

    if content.length <= length
      content
    else
      content[0..length-1] + "..."
    end
  end

  def estimated_send_time
    recipient_count = settings.dig("recipient_count") || 0
    send_rate_per_minute = 1000 # emails per minute

    minutes = (recipient_count.to_f / send_rate_per_minute).ceil
    [ minutes, 1 ].max # minimum 1 minute
  end

  def personalize_content_for(user_data = {})
    personalized_content = content.dup

    user_data.each do |key, value|
      personalized_content.gsub!("{{#{key}}}", value.to_s)
    end

    # Remove any remaining merge tags
    personalized_content.gsub!(/\{\{[^}]+\}\}/, "")

    personalized_content
  end

  def recipient_count
    settings.dig("recipient_count") || 0
  end

  def a_b_test_enabled?
    settings.dig("a_b_test", "enabled") == true
  end

  def scheduled?
    settings.dig("delivery_options", "scheduled_at").present?
  end

  def scheduled_at
    scheduled_time = settings.dig("delivery_options", "scheduled_at")
    return nil if scheduled_time.blank?

    Time.zone.parse(scheduled_time)
  rescue ArgumentError
    nil
  end

  # Advanced A/B Testing Methods
  def a_b_test_variants
    return [] unless a_b_test_enabled?

    settings.dig("a_b_test", "variants") || []
  end

  def a_b_test_split_percentage
    return 0 unless a_b_test_enabled?

    settings.dig("a_b_test", "split_percentage") || 50
  end

  def a_b_test_winner
    return nil unless a_b_test_enabled?

    settings.dig("a_b_test", "winner")
  end

  def a_b_test_results
    return {} unless a_b_test_enabled?

    variants = a_b_test_variants
    return {} if variants.empty?

    results = {}
    variants.each do |variant|
      variant_metrics = get_variant_metrics(variant["name"])
      results[variant["name"]] = {
        subject: variant["subject_line"],
        sent_count: variant_metrics[:sent],
        open_rate: variant_metrics[:open_rate],
        click_rate: variant_metrics[:click_rate],
        conversion_rate: variant_metrics[:conversion_rate],
        statistical_significance: calculate_statistical_significance(variant["name"])
      }
    end

    results
  end

  def declare_a_b_winner(variant_name, confidence_level = 95)
    return false unless a_b_test_enabled?
    return false unless a_b_test_variants.any? { |v| v["name"] == variant_name }

    # Update settings with winner
    new_settings = settings.dup
    new_settings["a_b_test"]["winner"] = variant_name
    new_settings["a_b_test"]["winner_declared_at"] = Time.current.iso8601
    new_settings["a_b_test"]["confidence_level"] = confidence_level

    update(settings: new_settings)
  end

  # Deliverability Analytics
  def deliverability_score
    total_sent = campaign.campaign_metrics.sum { |m| m.custom_metrics.dig("email", "total_sent") || 0 }
    total_bounces = campaign.campaign_metrics.sum(:email_bounces)
    spam_complaints = campaign.campaign_metrics.sum { |m| m.custom_metrics.dig("email", "spam_complaints") || 0 }

    return 100.0 if total_sent.zero?

    delivery_rate = ((total_sent - total_bounces - spam_complaints).to_f / total_sent * 100)
    [ delivery_rate, 0.0 ].max.round(2)
  end

  def bounce_analysis
    metrics = campaign.campaign_metrics.includes(:campaign)
    total_sent = metrics.sum { |m| m.custom_metrics.dig("email", "total_sent") || 0 }
    hard_bounces = metrics.sum { |m| m.custom_metrics.dig("email", "hard_bounces") || 0 }
    soft_bounces = metrics.sum { |m| m.custom_metrics.dig("email", "soft_bounces") || 0 }

    {
      total_bounces: hard_bounces + soft_bounces,
      hard_bounces: hard_bounces,
      soft_bounces: soft_bounces,
      hard_bounce_rate: total_sent > 0 ? (hard_bounces.to_f / total_sent * 100).round(2) : 0.0,
      soft_bounce_rate: total_sent > 0 ? (soft_bounces.to_f / total_sent * 100).round(2) : 0.0,
      bounce_categories: categorize_bounces
    }
  end

  def spam_analysis
    metrics = campaign.campaign_metrics.includes(:campaign)
    total_sent = metrics.sum { |m| m.custom_metrics.dig("email", "total_sent") || 0 }
    spam_complaints = metrics.sum { |m| m.custom_metrics.dig("email", "spam_complaints") || 0 }
    unsubscribes = metrics.sum { |m| m.custom_metrics.dig("email", "unsubscribes") || 0 }

    {
      spam_complaints: spam_complaints,
      spam_rate: total_sent > 0 ? (spam_complaints.to_f / total_sent * 100).round(4) : 0.0,
      unsubscribes: unsubscribes,
      unsubscribe_rate: total_sent > 0 ? (unsubscribes.to_f / total_sent * 100).round(2) : 0.0,
      list_hygiene_score: calculate_list_hygiene_score
    }
  end

  def inbox_placement_analysis
    metrics = campaign.campaign_metrics.includes(:campaign)
    total_delivered = metrics.sum { |m|
      total_sent = m.custom_metrics.dig("email", "total_sent") || 0
      bounces = m.email_bounces || 0
      total_sent - bounces
    }

    inbox_delivered = metrics.sum { |m| m.custom_metrics.dig("email", "inbox_delivered") || 0 }
    spam_folder = metrics.sum { |m| m.custom_metrics.dig("email", "spam_folder") || 0 }

    {
      inbox_rate: total_delivered > 0 ? (inbox_delivered.to_f / total_delivered * 100).round(2) : 0.0,
      spam_folder_rate: total_delivered > 0 ? (spam_folder.to_f / total_delivered * 100).round(2) : 0.0,
      total_delivered: total_delivered,
      inbox_delivered: inbox_delivered,
      spam_folder: spam_folder
    }
  end

  # Email Performance Optimization
  def optimal_send_time_analysis
    metrics = campaign.campaign_metrics
                     .joins("LEFT JOIN email_sends ON email_sends.campaign_metric_id = campaign_metrics.id")
                     .group("EXTRACT(hour FROM email_sends.sent_at)")
                     .average(:email_opens)

    return {} if metrics.empty?

    best_hour = metrics.max_by { |hour, opens| opens }&.first

    {
      hourly_performance: metrics,
      recommended_send_hour: best_hour,
      performance_variance: calculate_time_variance(metrics)
    }
  end

  def device_engagement_analysis
    device_data = campaign.campaign_metrics.sum { |m| m.custom_metrics.dig("email", "device_breakdown") || {} }

    return {} if device_data.empty?

    total_opens = device_data.values.sum

    device_data.transform_values do |opens|
      {
        opens: opens,
        percentage: total_opens > 0 ? (opens.to_f / total_opens * 100).round(2) : 0.0
      }
    end
  end

  def content_engagement_heatmap
    metrics = campaign.campaign_metrics
    link_data = metrics.map { |m| m.custom_metrics.dig("email", "link_clicks") || {} }
                      .reduce({}) { |acc, links| acc.merge(links) { |k, v1, v2| v1 + v2 } }

    total_clicks = link_data.values.sum

    link_data.transform_values do |clicks|
      {
        clicks: clicks,
        click_percentage: total_clicks > 0 ? (clicks.to_f / total_clicks * 100).round(2) : 0.0
      }
    end
  end

  private

  def valid_email_format?
    return false if from_email.blank?
    from_email.match?(URI::MailTo::EMAIL_REGEXP)
  end

  def get_variant_metrics(variant_name)
    metrics = campaign.campaign_metrics.includes(:campaign)

    variant_data = metrics.map { |m|
      m.custom_metrics.dig("a_b_test", "variants", variant_name) || {}
    }.reduce({}) { |acc, data| acc.merge(data) { |k, v1, v2| v1 + v2 } }

    {
      sent: variant_data["sent"] || 0,
      open_rate: variant_data["sent"] > 0 ? (variant_data["opens"].to_f / variant_data["sent"] * 100).round(2) : 0.0,
      click_rate: variant_data["sent"] > 0 ? (variant_data["clicks"].to_f / variant_data["sent"] * 100).round(2) : 0.0,
      conversion_rate: variant_data["sent"] > 0 ? (variant_data["conversions"].to_f / variant_data["sent"] * 100).round(2) : 0.0
    }
  end

  def calculate_statistical_significance(variant_name)
    return 0.0 unless a_b_test_variants.count >= 2

    control_variant = a_b_test_variants.first
    test_variant = a_b_test_variants.find { |v| v["name"] == variant_name }

    return 0.0 if control_variant.nil? || test_variant.nil?

    control_metrics = get_variant_metrics(control_variant["name"])
    test_metrics = get_variant_metrics(test_variant["name"])

    # Simple Z-test for conversion rate difference
    n1, n2 = control_metrics[:sent], test_metrics[:sent]
    p1 = control_metrics[:conversion_rate] / 100.0
    p2 = test_metrics[:conversion_rate] / 100.0

    return 0.0 if n1 == 0 || n2 == 0

    p_pool = ((p1 * n1) + (p2 * n2)) / (n1 + n2)
    se = Math.sqrt(p_pool * (1 - p_pool) * ((1.0/n1) + (1.0/n2)))

    return 0.0 if se == 0

    z_score = (p2 - p1).abs / se
    # Convert to confidence percentage (simplified)
    confidence = [ 95.0, z_score * 10 ].min
    confidence.round(2)
  end

  def categorize_bounces
    metrics = campaign.campaign_metrics.includes(:campaign)
    bounce_reasons = metrics.map { |m|
      m.custom_metrics.dig("email", "bounce_reasons") || {}
    }.reduce({}) { |acc, reasons| acc.merge(reasons) { |k, v1, v2| v1 + v2 } }

    {
      "invalid_email" => bounce_reasons["invalid_email"] || 0,
      "mailbox_full" => bounce_reasons["mailbox_full"] || 0,
      "server_error" => bounce_reasons["server_error"] || 0,
      "content_rejected" => bounce_reasons["content_rejected"] || 0,
      "other" => bounce_reasons["other"] || 0
    }
  end

  def calculate_list_hygiene_score
    total_sent = campaign.campaign_metrics.sum { |m| m.custom_metrics.dig("email", "total_sent") || 0 }
    return 100.0 if total_sent.zero?

    bounce_analysis_data = bounce_analysis
    spam_data = spam_analysis

    # Scoring factors (lower is better)
    hard_bounce_penalty = bounce_analysis_data[:hard_bounce_rate] * 2
    spam_penalty = spam_data[:spam_rate] * 10
    unsubscribe_penalty = spam_data[:unsubscribe_rate] * 0.5

    total_penalty = hard_bounce_penalty + spam_penalty + unsubscribe_penalty
    score = [ 100.0 - total_penalty, 0.0 ].max

    score.round(2)
  end

  def calculate_time_variance(hourly_metrics)
    return 0.0 if hourly_metrics.empty?

    values = hourly_metrics.values
    mean = values.sum.to_f / values.length
    variance = values.sum { |v| (v - mean) ** 2 } / values.length

    Math.sqrt(variance).round(2)
  end
end
