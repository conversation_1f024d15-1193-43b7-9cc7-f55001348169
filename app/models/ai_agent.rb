class AiAgent < ApplicationRecord
  belongs_to :tenant

  # Validations
  validates :name, presence: true, length: { minimum: 2, maximum: 100 }
  validates :agent_type, presence: true,
            inclusion: { in: %w[marketing_manager email_specialist social_media_agent
                               seo_specialist analytics_agent customer_experience content_creator
                               brand_manager automation_specialist],
                        message: "must be a valid agent type" }
  validates :description, presence: true, length: { minimum: 10, maximum: 500 }
  validates :status, presence: true,
            inclusion: { in: %w[active inactive training maintenance],
                        message: "must be active, inactive, training, or maintenance" }

  # Scopes
  scope :active, -> { where(status: "active") }
  scope :by_type, ->(type) { where(agent_type: type) }
  scope :for_tenant, ->(tenant) { where(tenant: tenant) }

  # Callbacks
  before_create :initialize_default_configuration
  before_create :initialize_default_metrics

  # Configuration helpers
  def configuration_value(key, default = nil)
    configuration&.dig(key.to_s) || default
  end

  def update_configuration(key, value)
    self.configuration ||= {}
    self.configuration[key.to_s] = value
    save!
  end

  # Metrics helpers
  def tasks_completed
    metrics&.dig("tasks_completed") || 0
  end

  def success_rate
    metrics&.dig("success_rate") || 0.0
  end

  def total_requests
    metrics&.dig("total_requests") || 0
  end

  def failed_requests
    metrics&.dig("failed_requests") || 0
  end

  def average_response_time
    metrics&.dig("average_response_time") || 0.0
  end

  def tasks_completed_today
    today_metrics = metrics&.dig("daily_stats", Date.current.to_s)
    today_metrics&.dig("tasks_completed") || 0
  end

  def last_active_at
    return nil unless metrics&.dig("last_active")
    Time.parse(metrics["last_active"]) rescue nil
  end

  def increment_task_completion!
    self.metrics ||= {}
    self.metrics["tasks_completed"] = tasks_completed + 1
    self.metrics["last_active"] = Time.current.iso8601
    save!
  end

  def update_success_rate!(success_count, total_count)
    return if total_count.zero?

    self.metrics ||= {}
    self.metrics["success_rate"] = (success_count.to_f / total_count * 100).round(1)
    save!
  end

  # Get a specific metric value
  def get_metric(key)
    metrics&.dig(key.to_s)
  end

  # Set a specific metric value
  def set_metric(key, value)
    self.metrics ||= {}
    self.metrics[key.to_s] = value
  end

  # Status helpers
  def active?
    status == "active"
  end

  def inactive?
    status == "inactive"
  end

  def training?
    status == "training"
  end

  def maintenance?
    status == "maintenance"
  end

  # Display helpers
  def display_color
    configuration_value("color", default_color_for_type)
  end

  def specialty
    configuration_value("specialty", default_specialty_for_type)
  end

  private

  def initialize_default_configuration
    self.configuration ||= {
      "color" => default_color_for_type,
      "specialty" => default_specialty_for_type,
      "max_concurrent_tasks" => 5,
      "retry_attempts" => 3,
      "timeout_seconds" => 300
    }
  end

  def initialize_default_metrics
    self.metrics ||= {
      "tasks_completed" => 0,
      "success_rate" => 0.0,
      "total_requests" => 0,
      "failed_requests" => 0,
      "average_response_time" => 0.0,
      "total_cost" => 0.0,
      "daily_stats" => {},
      "created_at" => Time.current.iso8601
    }
  end

  def default_color_for_type
    case agent_type
    when "marketing_manager" then "blue"
    when "email_specialist" then "green"
    when "social_media_agent" then "purple"
    when "seo_specialist" then "orange"
    when "analytics_agent" then "indigo"
    when "customer_experience" then "pink"
    when "content_creator" then "teal"
    when "brand_manager" then "red"
    when "automation_specialist" then "yellow"
    else "gray"
    end
  end

  def default_specialty_for_type
    case agent_type
    when "marketing_manager" then "Campaign Strategy"
    when "email_specialist" then "Email Marketing"
    when "social_media_agent" then "Social Media"
    when "seo_specialist" then "SEO & Content"
    when "analytics_agent" then "Data Analysis"
    when "customer_experience" then "CX Optimization"
    when "content_creator" then "Content Creation"
    when "brand_manager" then "Brand Management"
    when "automation_specialist" then "Marketing Automation"
    else "General Marketing"
    end
  end
end
