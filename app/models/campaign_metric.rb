# frozen_string_literal: true

# == Schema Information
# Table name: campaign_metrics
#
#  id                    :bigint           not null, primary key
#  campaign_id           :bigint           not null
#  metric_date           :date             not null
#  impressions           :integer          default(0)
#  clicks                :integer          default(0)
#  conversions           :integer          default(0)
#  revenue_cents         :integer          default(0)
#  cost_cents            :integer          default(0)
#  email_opens           :integer          default(0)
#  email_clicks          :integer          default(0)
#  email_bounces         :integer          default(0)
#  social_engagements    :integer          default(0)
#  social_shares         :integer          default(0)
#  social_comments       :integer          default(0)
#  seo_organic_traffic   :integer          default(0)
#  seo_keyword_rankings  :jsonb            default({}), not null
#  custom_metrics        :jsonb            default({}), not null
#  created_at            :datetime         not null
#  updated_at            :datetime         not null
#
# Indexes
#
#  index_campaign_metrics_on_campaign_id_and_date  (campaign_id,metric_date) UNIQUE
#  index_campaign_metrics_on_metric_date           (metric_date)
#  index_campaign_metrics_on_custom_metrics        (custom_metrics) USING gin
#  index_campaign_metrics_on_seo_keyword_rankings  (seo_keyword_rankings) USING gin
#
# Foreign Keys
#
#  fk_rails_...  (campaign_id => campaigns.id)
#

class CampaignMetric < ApplicationRecord
  # Associations
  belongs_to :campaign, required: true

  # Validations
  validates :metric_date, presence: true, uniqueness: { scope: :campaign_id }
  validates :impressions, :clicks, :conversions, :revenue_cents, :cost_cents,
            :email_opens, :email_clicks, :email_bounces,
            :social_engagements, :social_shares, :social_comments,
            :seo_organic_traffic,
            numericality: { greater_than_or_equal_to: 0 }

  # Scopes
  scope :for_date_range, ->(start_date, end_date) { where(metric_date: start_date..end_date) }
  scope :recent, ->(days = 30) { where(metric_date: days.days.ago..Date.current) }
  scope :this_month, -> { where(metric_date: Date.current.beginning_of_month..Date.current.end_of_month) }
  scope :last_month, -> { where(metric_date: 1.month.ago.beginning_of_month..1.month.ago.end_of_month) }

  # Delegate tenant access through campaign
  def tenant
    campaign.tenant
  end

  # Calculated metrics
  def click_through_rate
    return 0.0 if impressions.zero?
    (clicks.to_f / impressions * 100).round(2)
  end

  def conversion_rate
    return 0.0 if clicks.zero?
    (conversions.to_f / clicks * 100).round(2)
  end

  def engagement_rate
    # Calculate engagement rate as a combination of email engagement and social engagement
    total_interactions = email_opens + email_clicks + social_engagements + social_shares + social_comments
    total_reach = [ impressions, email_total_sent, social_total_reach ].max

    return 0.0 if total_reach.zero?
    (total_interactions.to_f / total_reach * 100).round(2)
  end

  def cost_per_click
    return 0.0 if clicks.zero?
    cost_in_dollars / clicks
  end

  def cost_per_conversion
    return 0.0 if conversions.zero?
    cost_in_dollars / conversions
  end

  def return_on_ad_spend
    return 0.0 if cost_cents.zero?
    (revenue_cents.to_f / cost_cents * 100).round(2)
  end

  def revenue_in_dollars
    revenue_cents / 100.0
  end

  def cost_in_dollars
    cost_cents / 100.0
  end

  def profit_in_dollars
    revenue_in_dollars - cost_in_dollars
  end

  # Customer Lifecycle Metrics
  def customer_acquisition_cost
    return 0.0 if conversions.zero?
    cost_in_dollars / conversions
  end

  def customer_lifetime_value
    # Estimate CLV based on conversion value and retention patterns
    avg_order_value = conversions > 0 ? revenue_in_dollars / conversions : 0.0
    retention_rate = custom_metrics.dig("retention_rate") || 0.85
    profit_margin = custom_metrics.dig("profit_margin") || 0.30

    return 0.0 if retention_rate.zero? || avg_order_value.zero?

    # Simple CLV calculation: AOV * Profit Margin * (Retention Rate / (1 - Retention Rate))
    clv = avg_order_value * profit_margin * (retention_rate / (1 - retention_rate))
    clv.round(2)
  end

  def ltv_to_cac_ratio
    cac = customer_acquisition_cost
    return 0.0 if cac.zero?

    clv = customer_lifetime_value
    (clv / cac).round(2)
  end

  def payback_period_days
    # Days to recover customer acquisition cost
    cac = customer_acquisition_cost
    return 0.0 if cac.zero?

    monthly_value = customer_lifetime_value / 12.0
    return 0.0 if monthly_value.zero?

    (cac / monthly_value * 30).round(0)
  end

  # Content Performance Metrics
  def content_engagement_score
    # Weighted score combining different engagement types
    email_weight = 0.3
    social_weight = 0.4
    conversion_weight = 0.3

    email_score = email_engagement_rate * email_weight
    social_score = social_engagement_rate * social_weight
    conversion_score = conversion_rate * conversion_weight

    ((email_score + social_score + conversion_score) * 10).round(1)
  end

  def viral_coefficient
    return 0.0 if social_engagements.zero?

    # Simple viral coefficient: shares per engagement
    (social_shares.to_f / social_engagements * 100).round(2)
  end

  def content_efficiency_score
    # Cost per unit of engagement
    total_engagements = email_opens + email_clicks + social_engagements + conversions
    return 0.0 if total_engagements.zero?

    efficiency = total_engagements.to_f / cost_in_dollars
    efficiency.round(2)
  end

  # Helper methods for engagement calculations
  def email_engagement_rate
    return 0.0 if email_total_sent.zero?
    total_email_engagement = email_opens + email_clicks
    (total_email_engagement.to_f / email_total_sent * 100).round(2)
  end

  def social_engagement_rate
    return 0.0 if social_total_reach.zero?
    (social_engagements.to_f / social_total_reach * 100).round(2)
  end

  # Email specific metrics
  def email_open_rate
    return 0.0 if email_total_sent.zero?
    (email_opens.to_f / email_total_sent * 100).round(2)
  end

  def email_click_rate
    return 0.0 if email_opens.zero?
    (email_clicks.to_f / email_opens * 100).round(2)
  end

  def email_bounce_rate
    return 0.0 if email_total_sent.zero?
    (email_bounces.to_f / email_total_sent * 100).round(2)
  end

  def email_total_sent
    custom_metrics.dig("email", "total_sent") || 0
  end

  # Social media metrics
  def social_engagement_rate
    return 0.0 if social_total_reach.zero?
    (social_engagements.to_f / social_total_reach * 100).round(2)
  end

  def social_total_reach
    custom_metrics.dig("social", "total_reach") || 0
  end

  def social_total_interactions
    social_engagements + social_shares + social_comments
  end

  # SEO metrics
  def average_keyword_position
    rankings = seo_keyword_rankings.values
    return 0.0 if rankings.empty?

    (rankings.sum.to_f / rankings.count).round(1)
  end

  def keywords_in_top_10
    seo_keyword_rankings.values.count { |position| position <= 10 }
  end

  def keywords_improved
    custom_metrics.dig("seo", "keywords_improved") || 0
  end

  # Aggregation methods
  def self.aggregate_for_campaign(campaign, start_date = nil, end_date = nil)
    metrics = campaign.campaign_metrics
    metrics = metrics.for_date_range(start_date, end_date) if start_date && end_date

    {
      total_impressions: metrics.sum(:impressions),
      total_clicks: metrics.sum(:clicks),
      total_conversions: metrics.sum(:conversions),
      total_revenue: metrics.sum(:revenue_cents) / 100.0,
      total_cost: metrics.sum(:cost_cents) / 100.0,
      average_ctr: calculate_average_ctr(metrics),
      average_conversion_rate: calculate_average_conversion_rate(metrics),
      total_email_opens: metrics.sum(:email_opens),
      total_email_clicks: metrics.sum(:email_clicks),
      total_social_engagements: metrics.sum(:social_engagements),
      total_seo_traffic: metrics.sum(:seo_organic_traffic)
    }
  end

  # Enhanced aggregation with lifecycle metrics
  def self.lifecycle_metrics_for_campaign(campaign, start_date = nil, end_date = nil)
    metrics = campaign.campaign_metrics
    metrics = metrics.for_date_range(start_date, end_date) if start_date && end_date

    return default_lifecycle_metrics if metrics.empty?

    total_conversions = metrics.sum(:conversions)
    total_cost = metrics.sum(:cost_cents) / 100.0
    total_revenue = metrics.sum(:revenue_cents) / 100.0

    # Calculate aggregate lifecycle metrics
    avg_cac = total_conversions > 0 ? (total_cost / total_conversions) : 0.0
    avg_clv = calculate_aggregate_clv(metrics)
    ltv_cac_ratio = avg_cac > 0 ? (avg_clv / avg_cac) : 0.0

    {
      customer_acquisition_cost: avg_cac.round(2),
      customer_lifetime_value: avg_clv.round(2),
      ltv_to_cac_ratio: ltv_cac_ratio.round(2),
      total_customers_acquired: total_conversions,
      payback_period_days: calculate_aggregate_payback_period(metrics),
      retention_rate: calculate_retention_rate(campaign, start_date, end_date)
    }
  end

  # Content performance aggregation
  def self.content_metrics_for_campaign(campaign, start_date = nil, end_date = nil)
    metrics = campaign.campaign_metrics
    metrics = metrics.for_date_range(start_date, end_date) if start_date && end_date

    return default_content_metrics if metrics.empty?

    {
      average_engagement_score: calculate_average_engagement_score(metrics),
      total_viral_shares: metrics.sum(:social_shares),
      average_viral_coefficient: calculate_average_viral_coefficient(metrics),
      content_efficiency_score: calculate_content_efficiency(metrics),
      email_performance: {
        total_opens: metrics.sum(:email_opens),
        total_clicks: metrics.sum(:email_clicks),
        average_open_rate: calculate_average_email_open_rate(metrics),
        average_click_rate: calculate_average_email_click_rate(metrics)
      },
      social_performance: {
        total_engagements: metrics.sum(:social_engagements),
        total_shares: metrics.sum(:social_shares),
        total_comments: metrics.sum(:social_comments),
        engagement_growth: calculate_engagement_growth(metrics)
      }
    }
  end

  def self.daily_summary(campaign, date = Date.current)
    metrics = campaign.campaign_metrics.find_by(metric_date: date)
    return default_summary if metrics.nil?

    {
      date: date,
      impressions: metrics.impressions,
      clicks: metrics.clicks,
      conversions: metrics.conversions,
      revenue: metrics.revenue_in_dollars,
      cost: metrics.cost_in_dollars,
      ctr: metrics.click_through_rate,
      conversion_rate: metrics.conversion_rate,
      roas: metrics.return_on_ad_spend
    }
  end

  private

  def self.calculate_average_ctr(metrics)
    total_impressions = metrics.sum(:impressions)
    total_clicks = metrics.sum(:clicks)
    return 0.0 if total_impressions.zero?

    (total_clicks.to_f / total_impressions * 100).round(2)
  end

  def self.calculate_average_conversion_rate(metrics)
    total_clicks = metrics.sum(:clicks)
    total_conversions = metrics.sum(:conversions)
    return 0.0 if total_clicks.zero?

    (total_conversions.to_f / total_clicks * 100).round(2)
  end

  # Lifecycle metric calculations
  def self.calculate_aggregate_clv(metrics)
    clv_values = metrics.map(&:customer_lifetime_value).reject(&:zero?)
    return 0.0 if clv_values.empty?

    clv_values.sum / clv_values.size
  end

  def self.calculate_aggregate_payback_period(metrics)
    payback_values = metrics.map(&:payback_period_days).reject(&:zero?)
    return 0.0 if payback_values.empty?

    (payback_values.sum / payback_values.size).round(0)
  end

  def self.calculate_retention_rate(campaign, start_date, end_date)
    # Simplified retention calculation - in production this would use customer journey data
    base_rate = 0.85
    campaign_age_factor = [ campaign.duration_in_days || 30, 1 ].max / 30.0

    # Adjust retention based on campaign performance
    performance_factor = campaign.roi > 200 ? 1.1 : (campaign.roi > 100 ? 1.0 : 0.9)

    adjusted_rate = base_rate * performance_factor * (0.95 ** campaign_age_factor)
    [ adjusted_rate, 0.1 ].max.round(3)
  end

  # Content metric calculations
  def self.calculate_average_engagement_score(metrics)
    scores = metrics.map(&:content_engagement_score).reject(&:zero?)
    return 0.0 if scores.empty?

    (scores.sum / scores.size).round(1)
  end

  def self.calculate_average_viral_coefficient(metrics)
    coefficients = metrics.map(&:viral_coefficient).reject(&:zero?)
    return 0.0 if coefficients.empty?

    (coefficients.sum / coefficients.size).round(2)
  end

  def self.calculate_content_efficiency(metrics)
    efficiencies = metrics.map(&:content_efficiency_score).reject(&:zero?)
    return 0.0 if efficiencies.empty?

    (efficiencies.sum / efficiencies.size).round(2)
  end

  def self.calculate_average_email_open_rate(metrics)
    total_sent = metrics.sum { |m| m.custom_metrics.dig("email", "total_sent") || 0 }
    total_opens = metrics.sum(:email_opens)
    return 0.0 if total_sent.zero?

    (total_opens.to_f / total_sent * 100).round(2)
  end

  def self.calculate_average_email_click_rate(metrics)
    total_opens = metrics.sum(:email_opens)
    total_clicks = metrics.sum(:email_clicks)
    return 0.0 if total_opens.zero?

    (total_clicks.to_f / total_opens * 100).round(2)
  end

  def self.calculate_engagement_growth(metrics)
    ordered_metrics = metrics.order(:metric_date)
    return 0.0 if ordered_metrics.count < 2

    first_metric = ordered_metrics.first
    last_metric = ordered_metrics.last

    first_engagement = first_metric.social_engagements
    last_engagement = last_metric.social_engagements

    return 0.0 if first_engagement.zero?

    growth_rate = ((last_engagement - first_engagement).to_f / first_engagement * 100)
    growth_rate.round(2)
  end

  # Default metric responses
  def self.default_lifecycle_metrics
    {
      customer_acquisition_cost: 0.0,
      customer_lifetime_value: 0.0,
      ltv_to_cac_ratio: 0.0,
      total_customers_acquired: 0,
      payback_period_days: 0,
      retention_rate: 0.0
    }
  end

  def self.default_content_metrics
    {
      average_engagement_score: 0.0,
      total_viral_shares: 0,
      average_viral_coefficient: 0.0,
      content_efficiency_score: 0.0,
      email_performance: {
        total_opens: 0,
        total_clicks: 0,
        average_open_rate: 0.0,
        average_click_rate: 0.0
      },
      social_performance: {
        total_engagements: 0,
        total_shares: 0,
        total_comments: 0,
        engagement_growth: 0.0
      }
    }
  end

  def self.default_summary
    {
      date: Date.current,
      impressions: 0,
      clicks: 0,
      conversions: 0,
      revenue: 0.0,
      cost: 0.0,
      ctr: 0.0,
      conversion_rate: 0.0,
      roas: 0.0
    }
  end
end
