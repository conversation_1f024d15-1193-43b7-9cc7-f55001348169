# frozen_string_literal: true

# == Schema Information
# Table name: users
#
#  id                     :bigint           not null, primary key
#  email                  :string           default(""), not null
#  encrypted_password     :string           default(""), not null
#  reset_password_token   :string
#  reset_password_sent_at :datetime
#  remember_created_at    :datetime
#  confirmation_token     :string
#  confirmed_at           :datetime
#  confirmation_sent_at   :datetime
#  unconfirmed_email      :string
#  tenant_id              :bigint           not null
#  first_name             :string           not null
#  last_name              :string           not null
#  role                   :integer          default(0), not null
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#
# Indexes
#
#  index_users_on_confirmation_token         (confirmation_token) UNIQUE
#  index_users_on_email_and_tenant_id        (email,tenant_id) UNIQUE
#  index_users_on_reset_password_token       (reset_password_token) UNIQUE
#  index_users_on_role                       (role)
#  index_users_on_tenant_id                  (tenant_id)
#
# Foreign Keys
#
#  fk_rails_...  (tenant_id => tenants.id)
#

class User < ApplicationRecord
  # Devise modules - using custom validations instead of :validatable
  devise :database_authenticatable, :registerable,
         :recoverable, :rememberable, :confirmable

  # Avatar attachment
  has_one_attached :avatar

  # Multi-tenancy
  acts_as_tenant(:tenant)
  belongs_to :tenant, required: true

  # Associations
  has_many :created_campaigns, class_name: "Campaign", foreign_key: "created_by_id", dependent: :destroy
  has_many :created_audiences, class_name: "Audience", foreign_key: "created_by_id", dependent: :destroy
  has_many :platform_configurations, dependent: :destroy

  # User settings and preferences
  has_one :user_preference, dependent: :destroy
  has_one :notification_setting, dependent: :destroy

  # Support system
  has_many :support_tickets, dependent: :destroy
  has_many :support_messages, dependent: :destroy
  has_many :assigned_tickets, class_name: "SupportTicket", foreign_key: "assigned_to_id", dependent: :nullify

  # Validations
  validates :first_name, presence: true
  validates :last_name, presence: true

  # Email validations (replacing Devise's :validatable)
  validates :email, presence: true,
                   uniqueness: { scope: :tenant_id, case_sensitive: false },
                   format: { with: URI::MailTo::EMAIL_REGEXP }

  # Password validations (replacing Devise's :validatable)
  validates :password, presence: true, length: { minimum: 6 }, if: :password_required?

  # Avatar validations
  validate :avatar_validation, if: -> { avatar.attached? }

  # Attribute declarations for enum fields
  attribute :role, :integer

  # Enums - using integer mapping for better performance
  enum :role, { member: 0, admin: 1, owner: 2 }

  # Instance methods
  def full_name
    "#{first_name} #{last_name}"
  end

  def admin_or_owner?
    admin? || owner?
  end

  private

  # Avatar validation method
  def avatar_validation
    return unless avatar.attached?

    # Check file type
    acceptable_types = %w[image/jpeg image/jpg image/png image/gif image/webp]
    unless acceptable_types.include?(avatar.content_type)
      errors.add(:avatar, "must be a valid image format (JPEG, PNG, GIF, WebP)")
    end

    # Check file size (10MB limit)
    if avatar.byte_size > 10.megabytes
      errors.add(:avatar, "must be less than 10MB")
    end
  end

  # Required for password validation
  def password_required?
    !persisted? || !password.nil? || !password_confirmation.nil?
  end
end
