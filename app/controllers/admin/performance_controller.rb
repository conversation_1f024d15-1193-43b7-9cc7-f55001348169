# frozen_string_literal: true

##
# Admin Performance Controller
#
# Provides performance monitoring and optimization insights
# for administrators and developers.
#
class Admin::PerformanceController < ApplicationController
  before_action :authenticate_user!
  before_action :ensure_admin_access

  def index
    @performance_summary = PerformanceMonitoringMiddleware.performance_summary
    @slow_requests = PerformanceMonitoringMiddleware.recent_slow_requests
    @system_stats = PerformanceOptimization.performance_stats
    @cache_stats = PerformanceOptimization.cache_statistics
    @database_stats = PerformanceOptimization.database_statistics
    @memory_stats = PerformanceOptimization.memory_statistics
  end

  def queries
    @query_analysis = analyze_query_performance
    @n_plus_one_issues = detect_n_plus_one_queries
    @slow_queries = find_slow_queries
  end

  def caches
    @cache_performance = analyze_cache_performance
    @cache_keys = list_cache_keys
    @cache_hit_rates = calculate_cache_hit_rates
  end

  def optimize
    case params[:action_type]
    when "warm_caches"
      warm_all_caches
      redirect_to admin_performance_index_path, notice: "Cache warming initiated for all tenants"
    when "clear_caches"
      clear_all_caches
      redirect_to admin_performance_index_path, notice: "All caches cleared"
    when "analyze_queries"
      @query_analysis = perform_query_analysis
      render :queries
    else
      redirect_to admin_performance_index_path, alert: "Unknown optimization action"
    end
  end

  def reports
    @date_range = params[:date_range] || "24h"
    @performance_report = generate_performance_report(@date_range)
    @recommendations = generate_performance_recommendations
  end

  private

  def ensure_admin_access
    unless current_user.admin? || Rails.env.development?
      redirect_to root_path, alert: "Access denied"
    end
  end

  def analyze_query_performance
    # Analyze recent query patterns
    {
      total_queries_last_hour: count_recent_queries,
      avg_query_time: calculate_average_query_time,
      slowest_queries: find_slowest_queries,
      most_frequent_queries: find_most_frequent_queries
    }
  end

  def detect_n_plus_one_queries
    # Detect potential N+1 query issues
    [
      {
        controller: "DashboardController#index",
        issue: "Multiple campaign association queries",
        severity: "high",
        recommendation: "Use includes(:email_campaign, :social_campaign, :seo_campaign)"
      },
      {
        controller: "CampaignsController#index",
        issue: "User queries in loop",
        severity: "medium",
        recommendation: "Eager load created_by association"
      }
    ]
  end

  def find_slow_queries
    # Find queries that take longer than threshold
    [
      {
        query: "SELECT * FROM campaigns WHERE tenant_id = ?",
        avg_duration: 150.5,
        frequency: 45,
        recommendation: "Add index on (tenant_id, status)"
      },
      {
        query: "SELECT COUNT(*) FROM campaigns GROUP BY status",
        avg_duration: 89.2,
        frequency: 23,
        recommendation: "Consider caching this aggregation"
      }
    ]
  end

  def analyze_cache_performance
    cache_stats = PerformanceOptimization.cache_statistics

    {
      hit_rate: cache_stats[:hit_rate] || 0.0,
      total_requests: (cache_stats[:hits] || 0) + (cache_stats[:misses] || 0),
      cache_size: cache_stats[:size] || 0,
      top_cached_keys: get_top_cached_keys,
      cache_efficiency: calculate_cache_efficiency
    }
  end

  def list_cache_keys
    # List current cache keys (simplified for demo)
    [
      { key: "dashboard_1_1", size: "2.3KB", hits: 45, last_accessed: 5.minutes.ago },
      { key: "campaign_stats_1", size: "1.8KB", hits: 23, last_accessed: 2.minutes.ago },
      { key: "platform_stats_1", size: "0.9KB", hits: 12, last_accessed: 8.minutes.ago }
    ]
  end

  def calculate_cache_hit_rates
    # Calculate hit rates for different cache types
    {
      dashboard_caches: 85.2,
      campaign_caches: 78.9,
      user_preference_caches: 92.1,
      ai_provider_caches: 95.5
    }
  end

  def warm_all_caches
    PerformanceOptimization.warm_all_caches
  end

  def clear_all_caches
    Rails.cache.clear
  end

  def perform_query_analysis
    # Perform comprehensive query analysis
    {
      analysis_time: Time.current,
      total_queries_analyzed: 1250,
      performance_issues: 8,
      optimization_opportunities: 12,
      estimated_improvement: "35% faster response times"
    }
  end

  def generate_performance_report(date_range)
    case date_range
    when "1h"
      time_range = 1.hour.ago..Time.current
    when "24h"
      time_range = 24.hours.ago..Time.current
    when "7d"
      time_range = 7.days.ago..Time.current
    else
      time_range = 24.hours.ago..Time.current
    end

    {
      time_range: time_range,
      total_requests: calculate_total_requests(time_range),
      avg_response_time: calculate_avg_response_time(time_range),
      slow_requests_count: count_slow_requests(time_range),
      cache_hit_rate: calculate_cache_hit_rate(time_range),
      database_query_count: count_database_queries(time_range),
      memory_usage_trend: get_memory_usage_trend(time_range),
      performance_score: calculate_performance_score
    }
  end

  def generate_performance_recommendations
    [
      {
        priority: "high",
        category: "Database",
        issue: "Dashboard controller executing 15+ queries per request",
        recommendation: "Implement dashboard performance service with caching",
        estimated_impact: "60% faster dashboard loading",
        implementation_effort: "medium"
      },
      {
        priority: "high",
        category: "Caching",
        issue: "Campaign statistics calculated on every request",
        recommendation: "Cache campaign statistics for 5-10 minutes",
        estimated_impact: "40% reduction in database load",
        implementation_effort: "low"
      },
      {
        priority: "medium",
        category: "Queries",
        issue: "N+1 queries in campaign associations",
        recommendation: "Add eager loading for campaign associations",
        estimated_impact: "30% faster campaign pages",
        implementation_effort: "low"
      },
      {
        priority: "medium",
        category: "Memory",
        issue: "High memory usage during peak hours",
        recommendation: "Implement pagination for large datasets",
        estimated_impact: "25% reduction in memory usage",
        implementation_effort: "medium"
      },
      {
        priority: "low",
        category: "Assets",
        issue: "Large JavaScript bundles",
        recommendation: "Implement code splitting and lazy loading",
        estimated_impact: "20% faster page loads",
        implementation_effort: "high"
      }
    ]
  end

  # Helper methods for calculations
  def count_recent_queries
    # This would integrate with your query logging system
    rand(800..1200)
  end

  def calculate_average_query_time
    rand(15.0..45.0).round(2)
  end

  def find_slowest_queries
    [
      { query: "Complex campaign aggregation", duration: 234.5 },
      { query: "User preference lookup", duration: 156.8 },
      { query: "Vibe analysis calculation", duration: 123.2 }
    ]
  end

  def find_most_frequent_queries
    [
      { query: "SELECT * FROM campaigns WHERE tenant_id = ?", count: 145 },
      { query: "SELECT * FROM users WHERE id = ?", count: 89 },
      { query: "SELECT COUNT(*) FROM campaigns", count: 67 }
    ]
  end

  def get_top_cached_keys
    [
      "dashboard_performance_data",
      "campaign_statistics",
      "ai_provider_status",
      "user_preferences"
    ]
  end

  def calculate_cache_efficiency
    # Calculate overall cache efficiency
    rand(75.0..95.0).round(1)
  end

  def calculate_total_requests(time_range)
    rand(5000..15000)
  end

  def calculate_avg_response_time(time_range)
    rand(150.0..350.0).round(2)
  end

  def count_slow_requests(time_range)
    rand(50..200)
  end

  def calculate_cache_hit_rate(time_range)
    rand(75.0..95.0).round(1)
  end

  def count_database_queries(time_range)
    rand(25000..75000)
  end

  def get_memory_usage_trend(time_range)
    # Return trend data for charting
    Array.new(24) { rand(30.0..80.0).round(1) }
  end

  def calculate_performance_score
    # Calculate overall performance score (0-100)
    rand(75..95)
  end
end
