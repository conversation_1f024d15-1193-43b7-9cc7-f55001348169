# frozen_string_literal: true

class AiAgentsController < ApplicationController
  before_action :authenticate_user!
  before_action :ensure_tenant_set

  def index
    @search_query = params[:q].to_s.strip
    @page = (params[:page] || 1).to_i
    @per_page = 4

    agents = get_active_agents
    if @search_query.present?
      agents = agents.select do |a|
        a[:name].downcase.include?(@search_query.downcase) ||
        a[:specialty].downcase.include?(@search_query.downcase)
      end
    end
    @total_agents = agents.size
    @active_agents = agents.slice((@page - 1) * @per_page, @per_page) || []
    @agent_performance = get_agent_performance
    @workflow_stats = get_workflow_stats

    # Activities pagination
    @activities_page = (params[:activities_page] || 1).to_i
    @activities_per_page = 5
    all_activities = get_recent_activities
    @total_activities = all_activities.size
    @recent_activities = all_activities.slice((@activities_page - 1) * @activities_per_page, @activities_per_page) || []
    @agent_configurations = get_agent_configurations
  end

  def show
    @agent = find_agent(params[:id])
    @agent_metrics = get_agent_metrics(@agent)
    @recent_tasks = get_agent_tasks(@agent)
  end

  private

  def ensure_tenant_set
    if current_user&.tenant
      ActsAsTenant.current_tenant = current_user.tenant
    else
      redirect_to root_path, alert: "Please contact support to set up your account."
    end
  end

  def current_tenant
    current_user.tenant
  end

  def get_active_agents
    current_tenant.ai_agents.active.map do |agent|
      {
        id: agent.agent_type,
        name: agent.name,
        description: agent.description,
        status: agent.status,
        tasks_completed: agent.tasks_completed,
        success_rate: agent.success_rate,
        specialty: agent.specialty,
        color: agent.display_color,
        last_active: agent.last_active_at
      }
    end
  end

  def get_agent_performance
    agents = current_tenant.ai_agents.active
    total_tasks = agents.sum(&:tasks_completed)
    total_requests = agents.sum(&:total_requests)
    failed_requests = agents.sum(&:failed_requests)

    {
      total_tasks_today: agents.sum { |a| a.tasks_completed_today },
      completed_tasks: total_tasks,
      success_rate: total_requests.positive? ? ((total_requests - failed_requests).to_f / total_requests * 100).round(1) : 0.0,
      average_response_time: agents.any? ? (agents.sum(&:average_response_time) / agents.count.to_f).round(1) : 0.0,
      cost_efficiency: calculate_cost_efficiency(agents),
      active_workflows: current_tenant.agent_workflows.where(status: "running").count
    }
  end

  def get_workflow_stats
    {
      total_workflows: current_tenant.agent_workflows.count,
      active_workflows: current_tenant.agent_workflows.where(status: "running").count,
      completed_today: current_tenant.agent_workflows.where(
        status: "completed",
        created_at: Date.current.beginning_of_day..Date.current.end_of_day
      ).count,
      success_rate: calculate_workflow_success_rate
    }
  end

  def get_recent_activities
    # Get recent agent workflows with enhanced data
    current_tenant.agent_workflows
                   .includes(:campaign)
                   .order(created_at: :desc)
                   .limit(10)
                   .map do |workflow|
      {
        id: workflow.id,
        agent_type: workflow.agent_type,
        action: workflow.workflow_config&.dig("action") || "Process Campaign",
        campaign_name: workflow.campaign&.name || "General Task",
        status: workflow.status,
        created_at: workflow.created_at,
        completion_time: workflow.completed_at
      }
    end
  end

  def get_agent_configurations
    {
      auto_optimization: true,
      smart_scheduling: true,
      cost_optimization: true,
      multi_model_routing: true,
      quality_assurance: true,
      real_time_monitoring: true
    }
  end

  def calculate_workflow_success_rate
    total = current_tenant.agent_workflows.count
    return 0 if total.zero?

    completed = current_tenant.agent_workflows.where(status: "completed").count
    (completed.to_f / total * 100).round(1)
  end

  def find_agent(agent_id)
    get_active_agents.find { |agent| agent[:id] == agent_id }
  end

  def get_agent_metrics(agent)
    {
      daily_tasks: rand(15..45),
      weekly_performance: rand(85..98),
      cost_per_task: rand(0.15..0.85).round(2),
      error_rate: rand(1..8),
      uptime: rand(95..99.9).round(1)
    }
  end

  def get_agent_tasks(agent)
    [
      {
        task: "Generate Email Content",
        status: "completed",
        duration: "2.3s",
        timestamp: 5.minutes.ago
      },
      {
        task: "Optimize Campaign Strategy",
        status: "running",
        duration: "45s",
        timestamp: 2.minutes.ago
      },
      {
        task: "Analyze Performance Metrics",
        status: "completed",
        duration: "1.8s",
        timestamp: 8.minutes.ago
      }
    ]
  end

  private

  def calculate_cost_efficiency(agents)
    return 0.0 if agents.empty?

    total_tasks = agents.sum(&:tasks_completed)
    total_cost = agents.sum { |agent| agent.get_metric("total_cost") || 0.0 }

    return 100.0 if total_cost.zero?

    # Calculate efficiency: tasks per dollar spent * 10 for percentage scale
    efficiency = (total_tasks.to_f / total_cost * 10).round(1)
    [ efficiency, 100.0 ].min # Cap at 100%
  end
end
