# frozen_string_literal: true

##
# Cache Warmup Job
#
# Background job to warm up caches for improved performance.
# Preloads frequently accessed data to reduce database load and improve response times.
#
class CacheWarmupJob < ApplicationJob
  queue_as :low_priority

  ##
  # Perform cache warmup for a specific tenant
  #
  # @param tenant_id [Integer] Tenant ID to warm cache for
  #
  def perform(tenant_id)
    tenant = Tenant.find(tenant_id)

    Rails.logger.info "Starting cache warmup for tenant #{tenant_id}"

    begin
      # Set tenant context for multi-tenancy
      ActsAsTenant.with_tenant(tenant) do
        # Warm up query optimization caches
        query_service = QueryOptimizationService.new(tenant)

        benchmark "Campaign statistics warmup" do
          query_service.campaign_statistics
        end

        benchmark "Platform statistics warmup" do
          query_service.platform_statistics
        end

        benchmark "Performance metrics warmup" do
          query_service.performance_metrics
        end

        benchmark "AI usage statistics warmup" do
          query_service.ai_usage_statistics
        end

        # Warm up dashboard caches for active users
        warm_dashboard_caches(tenant)

        # Also warm up existing campaign cache service if available
        if defined?(CampaignCacheService)
          cache_service = CampaignCacheService.new(tenant)
          cache_service.warm_cache_for_tenant(background: false)
        end
      end

      Rails.logger.info "Cache warmup completed for tenant #{tenant_id}"
    rescue => e
      Rails.logger.error "Cache warmup failed for tenant #{tenant_id}: #{e.message}"
      raise e
    end
  end

  private

  def warm_dashboard_caches(tenant)
    # Get active users from the last 24 hours
    active_users = tenant.users.where(
      "last_sign_in_at > ? OR current_sign_in_at > ?",
      24.hours.ago,
      24.hours.ago
    ).limit(10)

    active_users.each do |user|
      benchmark "Dashboard cache warmup for user #{user.id}" do
        performance_service = DashboardPerformanceService.new(tenant, user)

        # Trigger cache population
        performance_service.dashboard_data
        performance_service.vibe_data
        performance_service.dashboard_settings
      end
    end
  end

  def benchmark(message)
    start_time = Time.current
    result = yield
    end_time = Time.current
    duration = ((end_time - start_time) * 1000).round(2)

    Rails.logger.info "#{message}: #{duration}ms"
    result
  end
end
