# frozen_string_literal: true

class PredictiveAlertsService
  include Math

  def initialize(tenant = nil)
    @tenant = tenant || ActsAsTenant.current_tenant
  end

  def generate_predictive_alerts
    Rails.cache.fetch("predictive_alerts_#{@tenant.id}", expires_in: 5.minutes) do
      {
        performance_alerts: generate_performance_alerts,
        budget_alerts: generate_budget_alerts,
        audience_alerts: generate_audience_alerts,
        market_alerts: generate_market_alerts,
        technical_alerts: generate_technical_alerts,
        opportunity_alerts: generate_opportunity_alerts,
        risk_alerts: generate_risk_alerts,
        alert_summary: compile_alert_summary
      }
    end
  end

  def generate_performance_alerts
    alerts = []
    campaigns = fetch_active_campaigns

    campaigns.each do |campaign|
      # Performance decline prediction
      decline_prediction = predict_performance_decline(campaign)
      if decline_prediction[:risk_level] == "high"
        alerts << create_alert(
          type: "performance_decline",
          severity: "warning",
          campaign: campaign,
          prediction: decline_prediction,
          message: "Campaign '#{campaign.name}' predicted to decline by #{decline_prediction[:predicted_decline]}% in next 7 days",
          confidence: decline_prediction[:confidence],
          recommended_actions: decline_prediction[:recommendations],
          timeline: "7 days"
        )
      end

      # Conversion rate anomaly detection
      conversion_anomaly = detect_conversion_anomaly(campaign)
      if conversion_anomaly[:is_anomaly]
        alerts << create_alert(
          type: "conversion_anomaly",
          severity: conversion_anomaly[:severity],
          campaign: campaign,
          message: "Unusual conversion pattern detected for '#{campaign.name}': #{conversion_anomaly[:description]}",
          confidence: conversion_anomaly[:confidence],
          current_rate: conversion_anomaly[:current_rate],
          expected_rate: conversion_anomaly[:expected_rate],
          deviation: conversion_anomaly[:deviation],
          recommended_actions: conversion_anomaly[:actions]
        )
      end

      # Performance ceiling prediction
      ceiling_prediction = predict_performance_ceiling(campaign)
      if ceiling_prediction[:approaching_ceiling]
        alerts << create_alert(
          type: "performance_ceiling",
          severity: "info",
          campaign: campaign,
          message: "Campaign '#{campaign.name}' approaching performance ceiling. #{ceiling_prediction[:estimated_days]} days to plateau",
          confidence: ceiling_prediction[:confidence],
          current_performance: ceiling_prediction[:current_score],
          predicted_ceiling: ceiling_prediction[:ceiling_score],
          optimization_potential: ceiling_prediction[:optimization_suggestions]
        )
      end
    end

    alerts.sort_by { |alert| [ -alert[:confidence], alert[:severity] ] }
  end

  def generate_budget_alerts
    alerts = []
    campaigns = fetch_active_campaigns

    campaigns.each do |campaign|
      next unless campaign.budget_cents&.positive?

      # Budget depletion prediction
      depletion_prediction = predict_budget_depletion(campaign)
      if depletion_prediction[:needs_attention]
        severity = case depletion_prediction[:days_remaining]
        when 0..3
                    "critical"
        when 4..7
                    "warning"
        else
                    "info"
        end

        alerts << create_alert(
          type: "budget_depletion",
          severity: severity,
          campaign: campaign,
          message: "Budget for '#{campaign.name}' will be depleted in #{depletion_prediction[:days_remaining]} days",
          confidence: depletion_prediction[:confidence],
          current_spend: depletion_prediction[:current_spend],
          remaining_budget: depletion_prediction[:remaining_budget],
          daily_burn_rate: depletion_prediction[:daily_burn_rate],
          predicted_depletion_date: depletion_prediction[:depletion_date],
          recommended_actions: depletion_prediction[:recommendations]
        )
      end

      # Budget efficiency decline
      efficiency_analysis = analyze_budget_efficiency_trend(campaign)
      if efficiency_analysis[:declining_efficiency]
        alerts << create_alert(
          type: "budget_efficiency_decline",
          severity: "warning",
          campaign: campaign,
          message: "Budget efficiency declining for '#{campaign.name}': #{efficiency_analysis[:efficiency_change]}% decrease",
          confidence: efficiency_analysis[:confidence],
          current_cpa: efficiency_analysis[:current_cpa],
          trend_cpa: efficiency_analysis[:trend_cpa],
          efficiency_score: efficiency_analysis[:efficiency_score],
          optimization_opportunities: efficiency_analysis[:optimizations]
        )
      end

      # Overspend risk prediction
      overspend_risk = predict_overspend_risk(campaign)
      if overspend_risk[:risk_level] != "low"
        alerts << create_alert(
          type: "overspend_risk",
          severity: overspend_risk[:risk_level] == "high" ? "warning" : "info",
          campaign: campaign,
          message: "#{overspend_risk[:risk_level].capitalize} risk of overspend for '#{campaign.name}'",
          confidence: overspend_risk[:confidence],
          risk_factors: overspend_risk[:risk_factors],
          projected_overspend: overspend_risk[:projected_overspend],
          mitigation_strategies: overspend_risk[:mitigation_strategies]
        )
      end
    end

    alerts
  end

  def generate_audience_alerts
    alerts = []
    audiences = fetch_audiences_with_campaigns

    audiences.each do |audience|
      # Audience fatigue prediction
      fatigue_prediction = predict_audience_fatigue(audience)
      if fatigue_prediction[:fatigue_risk] == "high"
        alerts << create_alert(
          type: "audience_fatigue",
          severity: "warning",
          audience: audience,
          message: "High audience fatigue predicted for '#{audience.name}': #{fatigue_prediction[:engagement_decline]}% engagement decline",
          confidence: fatigue_prediction[:confidence],
          current_engagement: fatigue_prediction[:current_engagement],
          predicted_engagement: fatigue_prediction[:predicted_engagement],
          fatigue_indicators: fatigue_prediction[:indicators],
          refresh_recommendations: fatigue_prediction[:refresh_strategies]
        )
      end

      # Audience growth stagnation
      growth_analysis = analyze_audience_growth_trend(audience)
      if growth_analysis[:stagnation_risk]
        alerts << create_alert(
          type: "audience_growth_stagnation",
          severity: "info",
          audience: audience,
          message: "Audience growth stagnation detected for '#{audience.name}'",
          confidence: growth_analysis[:confidence],
          growth_rate: growth_analysis[:current_growth_rate],
          historical_average: growth_analysis[:historical_average],
          expansion_opportunities: growth_analysis[:expansion_opportunities]
        )
      end

      # Segment performance divergence
      segment_divergence = detect_segment_performance_divergence(audience)
      if segment_divergence[:significant_divergence]
        alerts << create_alert(
          type: "segment_divergence",
          severity: "info",
          audience: audience,
          message: "Significant performance divergence detected between segments in '#{audience.name}'",
          confidence: segment_divergence[:confidence],
          top_segment: segment_divergence[:top_performing_segment],
          bottom_segment: segment_divergence[:bottom_performing_segment],
          performance_gap: segment_divergence[:performance_gap],
          rebalancing_recommendations: segment_divergence[:rebalancing_suggestions]
        )
      end
    end

    alerts
  end

  def generate_market_alerts
    alerts = []

    # Seasonal trend predictions
    seasonal_predictions = predict_seasonal_trends
    if seasonal_predictions[:significant_changes]
      alerts << create_alert(
        type: "seasonal_trend",
        severity: "info",
        message: "Significant seasonal changes predicted: #{seasonal_predictions[:changes].join(', ')}",
        confidence: seasonal_predictions[:confidence],
        upcoming_season: seasonal_predictions[:upcoming_season],
        predicted_changes: seasonal_predictions[:predicted_changes],
        preparation_recommendations: seasonal_predictions[:recommendations],
        timeline: seasonal_predictions[:timeline]
      )
    end

    # Competitive pressure analysis
    competitive_analysis = analyze_competitive_pressure
    if competitive_analysis[:increased_pressure]
      alerts << create_alert(
        type: "competitive_pressure",
        severity: "warning",
        message: "Increased competitive pressure detected in #{competitive_analysis[:affected_segments].join(', ')}",
        confidence: competitive_analysis[:confidence],
        pressure_indicators: competitive_analysis[:indicators],
        affected_campaigns: competitive_analysis[:affected_campaigns],
        competitive_response_strategies: competitive_analysis[:response_strategies]
      )
    end

    # Market opportunity detection
    opportunity_analysis = detect_market_opportunities
    if opportunity_analysis[:opportunities].any?
      alerts << create_alert(
        type: "market_opportunity",
        severity: "info",
        message: "New market opportunities detected: #{opportunity_analysis[:opportunities].length} potential areas",
        confidence: opportunity_analysis[:confidence],
        opportunities: opportunity_analysis[:opportunities],
        investment_recommendations: opportunity_analysis[:investment_recommendations],
        expected_returns: opportunity_analysis[:expected_returns]
      )
    end

    alerts
  end

  def generate_technical_alerts
    alerts = []

    # Data quality issues prediction
    data_quality_check = predict_data_quality_issues
    if data_quality_check[:potential_issues]
      alerts << create_alert(
        type: "data_quality_degradation",
        severity: "warning",
        message: "Potential data quality issues detected: #{data_quality_check[:issue_types].join(', ')}",
        confidence: data_quality_check[:confidence],
        affected_metrics: data_quality_check[:affected_metrics],
        data_sources: data_quality_check[:problematic_sources],
        remediation_steps: data_quality_check[:remediation_recommendations]
      )
    end

    # Integration performance monitoring
    integration_health = monitor_integration_health
    if integration_health[:degraded_performance]
      alerts << create_alert(
        type: "integration_performance",
        severity: integration_health[:severity],
        message: "Integration performance degradation detected: #{integration_health[:affected_integrations].join(', ')}",
        confidence: integration_health[:confidence],
        performance_metrics: integration_health[:performance_metrics],
        impact_assessment: integration_health[:impact_assessment],
        optimization_recommendations: integration_health[:optimization_steps]
      )
    end

    # Capacity planning alerts
    capacity_analysis = analyze_capacity_requirements
    if capacity_analysis[:scaling_needed]
      alerts << create_alert(
        type: "capacity_scaling",
        severity: "info",
        message: "Capacity scaling recommended: #{capacity_analysis[:scaling_areas].join(', ')}",
        confidence: capacity_analysis[:confidence],
        current_utilization: capacity_analysis[:current_utilization],
        projected_demand: capacity_analysis[:projected_demand],
        scaling_recommendations: capacity_analysis[:scaling_recommendations],
        timeline: capacity_analysis[:recommended_timeline]
      )
    end

    alerts
  end

  def generate_opportunity_alerts
    alerts = []

    # Cross-sell/upsell opportunities
    cross_sell_analysis = identify_cross_sell_opportunities
    if cross_sell_analysis[:high_potential_opportunities].any?
      alerts << create_alert(
        type: "cross_sell_opportunity",
        severity: "info",
        message: "High-potential cross-sell opportunities identified: #{cross_sell_analysis[:high_potential_opportunities].length} prospects",
        confidence: cross_sell_analysis[:confidence],
        opportunities: cross_sell_analysis[:high_potential_opportunities],
        expected_revenue: cross_sell_analysis[:expected_revenue],
        implementation_strategy: cross_sell_analysis[:strategy_recommendations]
      )
    end

    # Content optimization opportunities
    content_optimization = identify_content_optimization_opportunities
    if content_optimization[:optimization_potential]
      alerts << create_alert(
        type: "content_optimization",
        severity: "info",
        message: "Content optimization opportunities identified with #{content_optimization[:potential_improvement]}% improvement potential",
        confidence: content_optimization[:confidence],
        underperforming_content: content_optimization[:underperforming_content],
        optimization_recommendations: content_optimization[:recommendations],
        expected_impact: content_optimization[:expected_impact]
      )
    end

    # Channel expansion opportunities
    channel_expansion = analyze_channel_expansion_opportunities
    if channel_expansion[:expansion_recommended]
      alerts << create_alert(
        type: "channel_expansion",
        severity: "info",
        message: "Channel expansion opportunities: #{channel_expansion[:recommended_channels].join(', ')}",
        confidence: channel_expansion[:confidence],
        recommended_channels: channel_expansion[:recommended_channels],
        investment_requirements: channel_expansion[:investment_requirements],
        expected_outcomes: channel_expansion[:expected_outcomes],
        implementation_roadmap: channel_expansion[:roadmap]
      )
    end

    alerts
  end

  def generate_risk_alerts
    alerts = []

    # Campaign performance risk assessment
    performance_risks = assess_campaign_performance_risks
    performance_risks[:high_risk_campaigns].each do |campaign_risk|
      alerts << create_alert(
        type: "campaign_performance_risk",
        severity: "warning",
        campaign_id: campaign_risk[:campaign_id],
        message: "High performance risk detected for campaign: #{campaign_risk[:risk_factors].join(', ')}",
        confidence: campaign_risk[:confidence],
        risk_score: campaign_risk[:risk_score],
        risk_factors: campaign_risk[:risk_factors],
        mitigation_strategies: campaign_risk[:mitigation_strategies],
        monitoring_recommendations: campaign_risk[:monitoring_recommendations]
      )
    end

    # Regulatory compliance risks
    compliance_risks = assess_regulatory_compliance_risks
    if compliance_risks[:compliance_issues].any?
      alerts << create_alert(
        type: "regulatory_compliance",
        severity: "critical",
        message: "Potential regulatory compliance issues detected: #{compliance_risks[:compliance_issues].length} areas of concern",
        confidence: compliance_risks[:confidence],
        compliance_areas: compliance_risks[:compliance_issues],
        regulatory_changes: compliance_risks[:upcoming_regulations],
        action_required: compliance_risks[:immediate_actions],
        compliance_recommendations: compliance_risks[:compliance_strategies]
      )
    end

    # Data privacy and security risks
    privacy_assessment = assess_data_privacy_risks
    if privacy_assessment[:privacy_risks].any?
      alerts << create_alert(
        type: "data_privacy_risk",
        severity: "warning",
        message: "Data privacy risks identified: #{privacy_assessment[:privacy_risks].length} potential issues",
        confidence: privacy_assessment[:confidence],
        privacy_risks: privacy_assessment[:privacy_risks],
        data_handling_issues: privacy_assessment[:data_handling_issues],
        remediation_steps: privacy_assessment[:remediation_steps],
        compliance_recommendations: privacy_assessment[:compliance_recommendations]
      )
    end

    alerts
  end

  def compile_alert_summary
    all_alerts = generate_predictive_alerts
    alert_types = all_alerts.except(:alert_summary)

    total_alerts = alert_types.values.flatten.length
    critical_alerts = alert_types.values.flatten.count { |alert| alert[:severity] == "critical" }
    warning_alerts = alert_types.values.flatten.count { |alert| alert[:severity] == "warning" }
    info_alerts = alert_types.values.flatten.count { |alert| alert[:severity] == "info" }

    high_confidence_alerts = alert_types.values.flatten.count { |alert| alert[:confidence] && alert[:confidence] > 80 }

    {
      total_alerts: total_alerts,
      alerts_by_severity: {
        critical: critical_alerts,
        warning: warning_alerts,
        info: info_alerts
      },
      high_confidence_alerts: high_confidence_alerts,
      alert_categories: alert_types.transform_values(&:length),
      priority_actions: extract_priority_actions(alert_types),
      trend_analysis: analyze_alert_trends(alert_types),
      overall_health_score: calculate_overall_health_score(alert_types)
    }
  end

  private

  def fetch_active_campaigns
    Campaign.where(tenant: @tenant, status: [ :active, :paused ])
           .includes(:campaign_metrics, :audiences)
  end

  def fetch_audiences_with_campaigns
    Audience.joins(:campaigns)
           .where(tenant: @tenant, campaigns: { status: [ :active, :paused ] })
           .includes(:campaigns, :audience_segments)
           .distinct
  end

  def create_alert(alert_data)
    {
      id: SecureRandom.uuid,
      timestamp: Time.current,
      tenant_id: @tenant.id,
      **alert_data
    }
  end

  def predict_performance_decline(campaign)
    metrics = campaign.campaign_metrics.recent(14).order(:metric_date)
    return { risk_level: "low", confidence: 0 } if metrics.count < 5

    # Calculate performance trend using linear regression
    performance_scores = metrics.map.with_index do |metric, index|
      score = calculate_daily_performance_score(metric)
      [ index, score ]
    end

    slope = calculate_linear_regression_slope(performance_scores)
    current_performance = performance_scores.last[1]

    # Predict performance in 7 days
    predicted_performance = current_performance + (slope * 7)
    decline_percentage = ((current_performance - predicted_performance) / current_performance * 100).round(2)

    risk_level = case decline_percentage
    when 20..Float::INFINITY
                  "high"
    when 10..19
                  "medium"
    else
                  "low"
    end

    confidence = calculate_prediction_confidence(performance_scores, slope)

    {
      risk_level: risk_level,
      predicted_decline: decline_percentage,
      confidence: confidence,
      current_score: current_performance.round(2),
      predicted_score: predicted_performance.round(2),
      recommendations: generate_decline_recommendations(decline_percentage, campaign)
    }
  end

  def detect_conversion_anomaly(campaign)
    metrics = campaign.campaign_metrics.recent(30).order(:metric_date)
    return { is_anomaly: false } if metrics.count < 7

    conversion_rates = metrics.map do |metric|
      metric.clicks > 0 ? (metric.conversions.to_f / metric.clicks * 100) : 0
    end

    # Use statistical anomaly detection
    mean = conversion_rates.sum / conversion_rates.length
    std_dev = calculate_standard_deviation(conversion_rates, mean)

    current_rate = conversion_rates.last
    z_score = std_dev > 0 ? ((current_rate - mean) / std_dev).abs : 0

    is_anomaly = z_score > 2 # 2 standard deviations
    severity = z_score > 3 ? "warning" : "info"

    confidence = [ z_score * 25, 95 ].min # Convert z-score to confidence percentage

    {
      is_anomaly: is_anomaly,
      severity: severity,
      confidence: confidence.round(2),
      current_rate: current_rate.round(2),
      expected_rate: mean.round(2),
      deviation: ((current_rate - mean) / mean * 100).round(2),
      z_score: z_score.round(2),
      description: generate_anomaly_description(current_rate, mean, z_score),
      actions: generate_anomaly_actions(current_rate, mean)
    }
  end

  def predict_performance_ceiling(campaign)
    metrics = campaign.campaign_metrics.recent(30).order(:metric_date)
    return { approaching_ceiling: false } if metrics.count < 10

    performance_scores = metrics.map.with_index { |metric, index| [ index, calculate_daily_performance_score(metric) ] }

    # Fit logistic growth curve to detect ceiling approach
    current_score = performance_scores.last[1]
    growth_rate = calculate_growth_rate(performance_scores)

    # Estimate ceiling using performance trajectory
    estimated_ceiling = estimate_performance_ceiling(performance_scores)
    ceiling_proximity = (current_score / estimated_ceiling * 100).round(2)

    approaching_ceiling = ceiling_proximity > 85 && growth_rate < 0.5

    days_to_ceiling = approaching_ceiling ? estimate_days_to_ceiling(current_score, estimated_ceiling, growth_rate) : nil

    {
      approaching_ceiling: approaching_ceiling,
      confidence: calculate_ceiling_confidence(performance_scores),
      current_score: current_score.round(2),
      ceiling_score: estimated_ceiling.round(2),
      ceiling_proximity: ceiling_proximity,
      estimated_days: days_to_ceiling,
      optimization_suggestions: generate_ceiling_optimization_suggestions(campaign, ceiling_proximity)
    }
  end

  def predict_budget_depletion(campaign)
    return { needs_attention: false } unless campaign.budget_cents&.positive?

    metrics = campaign.campaign_metrics.recent(14).order(:metric_date)
    return { needs_attention: false } if metrics.empty?

    total_spent = metrics.sum { |m| m.cost_cents }
    remaining_budget = campaign.budget_cents - total_spent

    return { needs_attention: false } if remaining_budget <= 0

    # Calculate daily burn rate
    daily_costs = metrics.group_by(&:metric_date).transform_values { |ms| ms.sum(&:cost_cents) }
    recent_daily_costs = daily_costs.values.last(7)

    return { needs_attention: false } if recent_daily_costs.empty?

    avg_daily_burn = recent_daily_costs.sum / recent_daily_costs.length
    days_remaining = avg_daily_burn > 0 ? (remaining_budget / avg_daily_burn).round : Float::INFINITY

    needs_attention = days_remaining <= 14
    confidence = calculate_budget_prediction_confidence(recent_daily_costs)

    {
      needs_attention: needs_attention,
      days_remaining: days_remaining == Float::INFINITY ? 999 : days_remaining,
      confidence: confidence,
      current_spend: (total_spent / 100.0).round(2),
      remaining_budget: (remaining_budget / 100.0).round(2),
      daily_burn_rate: (avg_daily_burn / 100.0).round(2),
      depletion_date: Time.current + days_remaining.days,
      recommendations: generate_budget_recommendations(days_remaining, campaign)
    }
  end

  def analyze_budget_efficiency_trend(campaign)
    metrics = campaign.campaign_metrics.recent(21).order(:metric_date)
    return { declining_efficiency: false } if metrics.count < 10

    # Calculate CPA (Cost Per Acquisition) trend
    daily_cpas = metrics.map do |metric|
      metric.conversions > 0 ? (metric.cost_cents / 100.0 / metric.conversions) : 0
    end.reject(&:zero?)

    return { declining_efficiency: false } if daily_cpas.count < 5

    recent_cpa = daily_cpas.last(7).sum / 7
    historical_cpa = daily_cpas.first(7).sum / 7

    efficiency_change = historical_cpa > 0 ? ((recent_cpa - historical_cpa) / historical_cpa * 100) : 0
    declining_efficiency = efficiency_change > 15 # 15% increase in CPA indicates declining efficiency

    efficiency_score = [ 100 - efficiency_change, 0 ].max

    {
      declining_efficiency: declining_efficiency,
      confidence: calculate_trend_confidence(daily_cpas),
      current_cpa: recent_cpa.round(2),
      trend_cpa: historical_cpa.round(2),
      efficiency_change: efficiency_change.round(2),
      efficiency_score: efficiency_score.round(2),
      optimizations: generate_efficiency_optimizations(efficiency_change, campaign)
    }
  end

  def predict_overspend_risk(campaign)
    return { risk_level: "low" } unless campaign.budget_cents&.positive?

    metrics = campaign.campaign_metrics.recent(14).order(:metric_date)
    return { risk_level: "low" } if metrics.empty?

    # Analyze spending patterns and acceleration
    daily_spending = metrics.group_by(&:metric_date).transform_values { |ms| ms.sum(&:cost_cents) }
    spending_values = daily_spending.values

    return { risk_level: "low" } if spending_values.empty?

    # Calculate spending acceleration
    spending_trend = calculate_spending_acceleration(spending_values)
    current_spend_rate = spending_values.last(3).sum / 3

    # Project spending to campaign end
    days_remaining = campaign.end_date ? (campaign.end_date - Date.current).to_i : 30
    projected_spend = current_spend_rate * days_remaining
    total_projected = metrics.sum(&:cost_cents) + projected_spend

    overspend_amount = total_projected - campaign.budget_cents
    overspend_percentage = (overspend_amount.to_f / campaign.budget_cents * 100)

    risk_level = case overspend_percentage
    when 20..Float::INFINITY
                  "high"
    when 5..19
                  "medium"
    else
                  "low"
    end

    {
      risk_level: risk_level,
      confidence: calculate_overspend_confidence(spending_values),
      projected_overspend: (overspend_amount / 100.0).round(2),
      overspend_percentage: overspend_percentage.round(2),
      risk_factors: identify_overspend_risk_factors(spending_trend, current_spend_rate),
      mitigation_strategies: generate_overspend_mitigation(risk_level, overspend_percentage)
    }
  end

  def predict_audience_fatigue(audience)
    campaigns = audience.campaigns.active.includes(:campaign_metrics)
    return { fatigue_risk: "low" } if campaigns.empty?

    all_metrics = campaigns.flat_map(&:campaign_metrics).sort_by(&:metric_date)
    return { fatigue_risk: "low" } if all_metrics.count < 10

    # Calculate engagement decline over time
    engagement_rates = all_metrics.map do |metric|
      metric.impressions > 0 ? ((metric.email_opens + metric.social_engagements).to_f / metric.impressions * 100) : 0
    end

    recent_engagement = engagement_rates.last(7).sum / 7
    historical_engagement = engagement_rates.first(7).sum / 7

    engagement_decline = historical_engagement > 0 ? ((historical_engagement - recent_engagement) / historical_engagement * 100) : 0

    fatigue_risk = case engagement_decline
    when 25..Float::INFINITY
                    "high"
    when 15..24
                    "medium"
    else
                    "low"
    end

    confidence = calculate_fatigue_confidence(engagement_rates)

    {
      fatigue_risk: fatigue_risk,
      confidence: confidence,
      engagement_decline: engagement_decline.round(2),
      current_engagement: recent_engagement.round(2),
      predicted_engagement: [ recent_engagement - engagement_decline, 0 ].max.round(2),
      indicators: identify_fatigue_indicators(engagement_rates, all_metrics),
      refresh_strategies: generate_refresh_strategies(fatigue_risk, audience)
    }
  end

  def analyze_audience_growth_trend(audience)
    campaigns = audience.campaigns.includes(:campaign_metrics)
    return { stagnation_risk: false } if campaigns.empty?

    # Analyze audience reach growth over time
    monthly_reach = campaigns.flat_map(&:campaign_metrics)
                            .group_by { |m| m.metric_date.beginning_of_month }
                            .transform_values { |metrics| metrics.sum(&:impressions) }

    return { stagnation_risk: false } if monthly_reach.count < 3

    reach_values = monthly_reach.values
    growth_rates = reach_values.each_cons(2).map { |prev, curr| ((curr - prev).to_f / prev * 100) }

    avg_growth_rate = growth_rates.sum / growth_rates.length
    recent_growth = growth_rates.last(2).sum / 2

    stagnation_risk = avg_growth_rate < 5 && recent_growth < 2

    {
      stagnation_risk: stagnation_risk,
      confidence: calculate_growth_confidence(growth_rates),
      current_growth_rate: recent_growth.round(2),
      historical_average: avg_growth_rate.round(2),
      expansion_opportunities: identify_expansion_opportunities(audience, stagnation_risk)
    }
  end

  def detect_segment_performance_divergence(audience)
    segments = audience.audience_segments.includes(audience: { campaigns: :campaign_metrics })
    return { significant_divergence: false } if segments.count < 2

    segment_performances = segments.map do |segment|
      campaigns = segment.audience.campaigns
      metrics = campaigns.flat_map(&:campaign_metrics)

      avg_conversion_rate = metrics.empty? ? 0 :
        (metrics.sum(&:conversions).to_f / [ metrics.sum(&:clicks), 1 ].max * 100)

      {
        segment: segment,
        performance: avg_conversion_rate
      }
    end

    performances = segment_performances.map { |sp| sp[:performance] }
    performance_variance = calculate_variance(performances)

    # Check if variance is significant
    significant_divergence = performance_variance > 50 # Threshold for significant divergence

    top_performer = segment_performances.max_by { |sp| sp[:performance] }
    bottom_performer = segment_performances.min_by { |sp| sp[:performance] }

    performance_gap = top_performer[:performance] - bottom_performer[:performance]

    {
      significant_divergence: significant_divergence,
      confidence: calculate_divergence_confidence(performances),
      performance_variance: performance_variance.round(2),
      top_performing_segment: top_performer[:segment].name,
      bottom_performing_segment: bottom_performer[:segment].name,
      performance_gap: performance_gap.round(2),
      rebalancing_suggestions: generate_rebalancing_suggestions(segment_performances)
    }
  end

  # Helper methods for calculations
  def calculate_daily_performance_score(metric)
    # Composite performance score based on multiple factors
    ctr = metric.impressions > 0 ? (metric.clicks.to_f / metric.impressions * 100) : 0
    conversion_rate = metric.clicks > 0 ? (metric.conversions.to_f / metric.clicks * 100) : 0
    roi = metric.cost_cents > 0 ? ((metric.revenue_cents - metric.cost_cents).to_f / metric.cost_cents * 100) : 0

    # Weighted score
    (ctr * 0.3 + conversion_rate * 0.4 + [ roi / 10, 100 ].min * 0.3).round(2)
  end

  def calculate_linear_regression_slope(data_points)
    n = data_points.length
    return 0 if n < 2

    sum_x = data_points.sum { |point| point[0] }
    sum_y = data_points.sum { |point| point[1] }
    sum_xy = data_points.sum { |point| point[0] * point[1] }
    sum_x2 = data_points.sum { |point| point[0] ** 2 }

    denominator = (n * sum_x2 - sum_x ** 2)
    return 0 if denominator == 0

    (n * sum_xy - sum_x * sum_y).to_f / denominator
  end

  def calculate_prediction_confidence(data_points, slope)
    return 0 if data_points.length < 3

    # Calculate R-squared for confidence
    mean_y = data_points.sum { |point| point[1] } / data_points.length
    ss_res = data_points.sum { |point| (point[1] - (slope * point[0])) ** 2 }
    ss_tot = data_points.sum { |point| (point[1] - mean_y) ** 2 }

    r_squared = ss_tot > 0 ? (1 - ss_res / ss_tot) : 0
    [ r_squared * 100, 95 ].min.round(2)
  end

  def calculate_standard_deviation(values, mean)
    return 0 if values.length < 2

    variance = values.sum { |value| (value - mean) ** 2 } / values.length
    sqrt(variance)
  end

  def generate_decline_recommendations(decline_percentage, campaign)
    recommendations = []

    if decline_percentage > 25
      recommendations << "Immediate campaign review and optimization required"
      recommendations << "Consider audience refresh or creative rotation"
    elsif decline_percentage > 15
      recommendations << "Monitor closely and prepare optimization strategies"
      recommendations << "A/B test new creative variations"
    else
      recommendations << "Continue monitoring performance trends"
    end

    recommendations
  end

  def generate_anomaly_description(current_rate, mean, z_score)
    deviation_type = current_rate > mean ? "above" : "below"
    magnitude = case z_score
    when 0..2
                 "moderate"
    when 2..3
                 "significant"
    else
                 "extreme"
    end

    "#{magnitude.capitalize} deviation #{deviation_type} expected performance"
  end

  def generate_anomaly_actions(current_rate, mean)
    actions = []

    if current_rate < mean * 0.5
      actions << "Investigate potential technical issues"
      actions << "Review audience targeting"
      actions << "Check creative performance"
    elsif current_rate > mean * 1.5
      actions << "Analyze high performance drivers"
      actions << "Scale successful elements"
      actions << "Document optimization insights"
    end

    actions
  end

  def estimate_performance_ceiling(performance_scores)
    # Simple ceiling estimation using recent performance peaks
    recent_scores = performance_scores.last(10).map { |point| point[1] }
    recent_scores.max * 1.1 # 10% buffer above recent peak
  end

  def calculate_growth_rate(performance_scores)
    return 0 if performance_scores.length < 2

    first_score = performance_scores.first(5).sum { |point| point[1] } / 5
    last_score = performance_scores.last(5).sum { |point| point[1] } / 5

    first_score > 0 ? ((last_score - first_score) / first_score * 100) : 0
  end

  def estimate_days_to_ceiling(current_score, ceiling_score, growth_rate)
    return nil if growth_rate <= 0

    score_gap = ceiling_score - current_score
    daily_growth = growth_rate / 30 # Convert monthly to daily growth rate

    (score_gap / daily_growth).round
  end

  def calculate_ceiling_confidence(performance_scores)
    # Confidence based on data consistency and trend strength
    recent_variance = calculate_variance(performance_scores.last(10).map { |point| point[1] })
    consistency_score = [ 100 - recent_variance, 0 ].max

    [ consistency_score, 85 ].min.round(2)
  end

  def generate_ceiling_optimization_suggestions(campaign, ceiling_proximity)
    suggestions = []

    if ceiling_proximity > 90
      suggestions << "Explore new audience segments"
      suggestions << "Test different creative approaches"
      suggestions << "Consider budget reallocation"
    elsif ceiling_proximity > 80
      suggestions << "Implement advanced targeting options"
      suggestions << "Optimize bidding strategies"
      suggestions << "Test new ad formats"
    end

    suggestions
  end

  def calculate_budget_prediction_confidence(recent_costs)
    return 0 if recent_costs.length < 3

    mean_cost = recent_costs.sum / recent_costs.length
    variance = recent_costs.sum { |cost| (cost - mean_cost) ** 2 } / recent_costs.length
    coefficient_of_variation = mean_cost > 0 ? sqrt(variance) / mean_cost : 0

    # Lower CV means higher confidence
    confidence = [ 100 - (coefficient_of_variation * 100), 10 ].max
    confidence.round(2)
  end

  def generate_budget_recommendations(days_remaining, campaign)
    recommendations = []

    case days_remaining
    when 0..3
      recommendations << "Immediate budget adjustment required"
      recommendations << "Consider campaign pause or budget increase"
    when 4..7
      recommendations << "Plan budget extension or campaign optimization"
      recommendations << "Review spend allocation across ad sets"
    when 8..14
      recommendations << "Monitor spending closely"
      recommendations << "Prepare budget adjustment strategy"
    end

    recommendations
  end

  def calculate_trend_confidence(data_points)
    return 0 if data_points.length < 5

    # Use coefficient of determination for trend confidence
    mean = data_points.sum / data_points.length
    ss_tot = data_points.sum { |point| (point - mean) ** 2 }

    # Simple trend line calculation
    x_values = (0...data_points.length).to_a
    slope = calculate_linear_regression_slope(x_values.zip(data_points))

    predicted_values = x_values.map { |x| slope * x + data_points.first }
    ss_res = data_points.zip(predicted_values).sum { |actual, predicted| (actual - predicted) ** 2 }

    r_squared = ss_tot > 0 ? (1 - ss_res / ss_tot) : 0
    [ r_squared * 100, 90 ].min.round(2)
  end

  def generate_efficiency_optimizations(efficiency_change, campaign)
    optimizations = []

    if efficiency_change > 20
      optimizations << "Review and optimize targeting parameters"
      optimizations << "Pause underperforming ad sets"
      optimizations << "Implement bid adjustments"
    elsif efficiency_change > 10
      optimizations << "A/B test new creative elements"
      optimizations << "Adjust audience targeting"
      optimizations << "Review keyword performance"
    end

    optimizations
  end

  def calculate_spending_acceleration(spending_values)
    return 0 if spending_values.length < 3

    # Calculate second derivative to detect acceleration
    first_differences = spending_values.each_cons(2).map { |a, b| b - a }
    second_differences = first_differences.each_cons(2).map { |a, b| b - a }

    second_differences.sum / second_differences.length
  end

  def calculate_overspend_confidence(spending_values)
    # Confidence based on spending pattern consistency
    mean_spending = spending_values.sum / spending_values.length
    variance = spending_values.sum { |spend| (spend - mean_spending) ** 2 } / spending_values.length
    cv = mean_spending > 0 ? sqrt(variance) / mean_spending : 0

    confidence = [ 100 - (cv * 50), 20 ].max
    confidence.round(2)
  end

  def identify_overspend_risk_factors(spending_trend, current_spend_rate)
    factors = []

    factors << "Accelerating spend rate" if spending_trend > 0
    factors << "High daily burn rate" if current_spend_rate > 1000 # Example threshold
    factors << "Insufficient budget monitoring"

    factors
  end

  def generate_overspend_mitigation(risk_level, overspend_percentage)
    strategies = []

    case risk_level
    when "high"
      strategies << "Implement immediate spend controls"
      strategies << "Reduce bid amounts across all ad sets"
      strategies << "Pause lowest-performing campaigns"
    when "medium"
      strategies << "Increase monitoring frequency"
      strategies << "Set up automated spend alerts"
      strategies << "Review campaign pacing settings"
    end

    strategies
  end

  def calculate_fatigue_confidence(engagement_rates)
    return 0 if engagement_rates.length < 7

    # Look for consistent decline pattern
    recent_rates = engagement_rates.last(7)
    decline_consistency = 0

    recent_rates.each_cons(2) do |prev, curr|
      decline_consistency += 1 if curr < prev
    end

    confidence = (decline_consistency.to_f / 6 * 100).round(2)
    [ confidence, 85 ].min
  end

  def identify_fatigue_indicators(engagement_rates, metrics)
    indicators = []

    # Check for declining engagement
    recent_avg = engagement_rates.last(7).sum / 7
    historical_avg = engagement_rates.first(7).sum / 7

    indicators << "Declining engagement rate" if recent_avg < historical_avg * 0.8

    # Check for increasing frequency without proportional response
    campaign_frequency = metrics.length.to_f / 30 # campaigns per day
    indicators << "High campaign frequency" if campaign_frequency > 0.5

    # Check for creative staleness
    indicators << "Creative refresh needed" if metrics.length > 20

    indicators
  end

  def generate_refresh_strategies(fatigue_risk, audience)
    strategies = []

    case fatigue_risk
    when "high"
      strategies << "Implement immediate creative refresh"
      strategies << "Reduce campaign frequency"
      strategies << "Expand to new audience segments"
    when "medium"
      strategies << "Plan creative rotation schedule"
      strategies << "Test new messaging approaches"
      strategies << "Review audience targeting criteria"
    end

    strategies
  end

  def calculate_growth_confidence(growth_rates)
    return 0 if growth_rates.empty?

    # Confidence based on growth rate consistency
    mean_growth = growth_rates.sum / growth_rates.length
    variance = growth_rates.sum { |rate| (rate - mean_growth) ** 2 } / growth_rates.length
    cv = mean_growth.abs > 0 ? sqrt(variance) / mean_growth.abs : 0

    confidence = [ 100 - (cv * 50), 20 ].max
    confidence.round(2)
  end

  def identify_expansion_opportunities(audience, stagnation_risk)
    opportunities = []

    if stagnation_risk
      opportunities << "Lookalike audience expansion"
      opportunities << "Geographic expansion"
      opportunities << "Interest-based targeting expansion"
      opportunities << "Demographic expansion"
    else
      opportunities << "Gradual audience scaling"
      opportunities << "Performance-based expansion"
    end

    opportunities
  end

  def calculate_variance(values)
    return 0 if values.length < 2

    mean = values.sum / values.length
    variance = values.sum { |value| (value - mean) ** 2 } / values.length
    variance
  end

  def calculate_divergence_confidence(performances)
    return 0 if performances.length < 2

    # Use F-test concept for variance significance
    variance = calculate_variance(performances)
    mean_performance = performances.sum / performances.length

    # Normalized confidence based on variance relative to mean
    cv = mean_performance > 0 ? sqrt(variance) / mean_performance : 0
    confidence = [ cv * 100, 95 ].min.round(2)

    confidence
  end

  def generate_rebalancing_suggestions(segment_performances)
    suggestions = []

    top_segments = segment_performances.sort_by { |sp| -sp[:performance] }.first(2)
    bottom_segments = segment_performances.sort_by { |sp| sp[:performance] }.first(2)

    suggestions << "Increase budget allocation to #{top_segments.first[:segment].name}"
    suggestions << "Optimize or reduce focus on #{bottom_segments.first[:segment].name}"
    suggestions << "Analyze success factors from top-performing segments"

    suggestions
  end

  # Placeholder methods for market, technical, opportunity, and risk alerts
  def predict_seasonal_trends
    { significant_changes: false, confidence: 0 }
  end

  def analyze_competitive_pressure
    { increased_pressure: false, confidence: 0 }
  end

  def detect_market_opportunities
    { opportunities: [], confidence: 0 }
  end

  def predict_data_quality_issues
    { potential_issues: false, confidence: 0 }
  end

  def monitor_integration_health
    { degraded_performance: false, confidence: 0 }
  end

  def analyze_capacity_requirements
    { scaling_needed: false, confidence: 0 }
  end

  def identify_cross_sell_opportunities
    { high_potential_opportunities: [], confidence: 0 }
  end

  def identify_content_optimization_opportunities
    { optimization_potential: false, confidence: 0 }
  end

  def analyze_channel_expansion_opportunities
    { expansion_recommended: false, confidence: 0 }
  end

  def assess_campaign_performance_risks
    { high_risk_campaigns: [] }
  end

  def assess_regulatory_compliance_risks
    { compliance_issues: [], confidence: 0 }
  end

  def assess_data_privacy_risks
    { privacy_risks: [], confidence: 0 }
  end

  def extract_priority_actions(alert_types)
    critical_alerts = alert_types.values.flatten.select { |alert| alert[:severity] == "critical" }
    warning_alerts = alert_types.values.flatten.select { |alert| alert[:severity] == "warning" }

    actions = []
    actions.concat(critical_alerts.map { |alert| { action: alert[:message], priority: "critical" } })
    actions.concat(warning_alerts.first(3).map { |alert| { action: alert[:message], priority: "high" } })

    actions.first(5)
  end

  def analyze_alert_trends(alert_types)
    total_alerts = alert_types.values.flatten.length

    {
      trend_direction: total_alerts > 10 ? "increasing" : "stable",
      most_common_type: alert_types.max_by { |_, alerts| alerts.length }&.first,
      alert_frequency: "#{total_alerts} alerts generated"
    }
  end

  def calculate_overall_health_score(alert_types)
    total_alerts = alert_types.values.flatten.length
    critical_alerts = alert_types.values.flatten.count { |alert| alert[:severity] == "critical" }
    warning_alerts = alert_types.values.flatten.count { |alert| alert[:severity] == "warning" }

    # Health score calculation (higher is better)
    base_score = 100
    critical_penalty = critical_alerts * 20
    warning_penalty = warning_alerts * 10
    info_penalty = (total_alerts - critical_alerts - warning_alerts) * 2

    health_score = [ base_score - critical_penalty - warning_penalty - info_penalty, 0 ].max
    health_score.round(2)
  end
end
