# frozen_string_literal: true

##
# Query Optimization Service
#
# Provides optimized database queries with proper eager loading,
# strategic caching, and performance monitoring.
#
class QueryOptimizationService
  include ActiveSupport::Benchmarkable

  attr_reader :tenant

  def initialize(tenant)
    @tenant = tenant
  end

  ##
  # Get campaigns with all associations preloaded
  #
  # @param filters [Hash] Filter options
  # @return [ActiveRecord::Relation] Optimized campaigns query
  #
  def campaigns_with_associations(filters = {})
    benchmark "campaigns_with_associations" do
      campaigns = base_campaigns_query

      # Apply filters using indexed columns
      campaigns = apply_filters(campaigns, filters)

      # Eager load all necessary associations to prevent N+1 queries
      campaigns.includes(
        :email_campaign,
        :social_campaign,
        :seo_campaign,
        :campaign_metrics,
        :vibe_analysis_records,
        :authenticity_checks,
        created_by: [ :user_preference ]
      )
    end
  end

  ##
  # Get campaign statistics in a single optimized query
  #
  # @return [Hash] Campaign statistics
  #
  def campaign_statistics
    Rails.cache.fetch(stats_cache_key, expires_in: 10.minutes) do
      benchmark "campaign_statistics" do
        # Single query to get all status counts and budget sums
        stats = tenant.campaigns
                     .group(:status)
                     .group(:campaign_type)
                     .select(
                       "status",
                       "campaign_type",
                       "COUNT(*) as count",
                       "SUM(budget_cents) as total_budget_cents"
                     )

        process_campaign_statistics(stats)
      end
    end
  end

  ##
  # Get platform distribution with optimized joins
  #
  # @return [Hash] Platform statistics
  #
  def platform_statistics
    Rails.cache.fetch(platform_cache_key, expires_in: 15.minutes) do
      benchmark "platform_statistics" do
        # Use EXISTS queries for better performance than JOINs
        {
          email_campaigns: tenant.campaigns.where(
            "EXISTS (SELECT 1 FROM email_campaigns WHERE email_campaigns.campaign_id = campaigns.id)"
          ).count,

          social_campaigns: tenant.campaigns.where(
            "EXISTS (SELECT 1 FROM social_campaigns WHERE social_campaigns.campaign_id = campaigns.id)"
          ).count,

          seo_campaigns: tenant.campaigns.where(
            "EXISTS (SELECT 1 FROM seo_campaigns WHERE seo_campaigns.campaign_id = campaigns.id)"
          ).count,

          multi_channel: tenant.campaigns.where(
            "EXISTS (SELECT 1 FROM email_campaigns WHERE email_campaigns.campaign_id = campaigns.id) AND " \
            "EXISTS (SELECT 1 FROM social_campaigns WHERE social_campaigns.campaign_id = campaigns.id)"
          ).count
        }
      end
    end
  end

  ##
  # Get performance metrics with optimized calculations
  #
  # @return [Hash] Performance metrics
  #
  def performance_metrics
    Rails.cache.fetch(performance_cache_key, expires_in: 5.minutes) do
      benchmark "performance_metrics" do
        # Use raw SQL for complex aggregations
        result = ActiveRecord::Base.connection.execute(performance_metrics_sql)

        if result.any?
          row = result.first
          {
            total_campaigns: row["total_campaigns"].to_i,
            active_campaigns: row["active_campaigns"].to_i,
            completed_campaigns: row["completed_campaigns"].to_i,
            total_budget: (row["total_budget"].to_f / 100.0).round(2),
            active_budget: (row["active_budget"].to_f / 100.0).round(2),
            avg_campaign_duration: row["avg_duration"].to_f.round(1),
            success_rate: calculate_success_rate(row)
          }
        else
          default_performance_metrics
        end
      end
    end
  end

  ##
  # Get AI usage statistics with date range optimization
  #
  # @return [Hash] AI usage statistics
  #
  def ai_usage_statistics
    Rails.cache.fetch(ai_usage_cache_key, expires_in: 5.minutes) do
      benchmark "ai_usage_statistics" do
        # Use date partitioning for better performance
        current_month_start = Time.current.beginning_of_month
        thirty_days_ago = 30.days.ago

        usage_stats = tenant.ai_usage_records
                           .where(created_at: thirty_days_ago..Time.current)
                           .group(
                             "CASE WHEN created_at >= '#{current_month_start}' THEN 'current_month' ELSE 'previous_days' END"
                           )
                           .sum(:cost)

        {
          total_cost: usage_stats.values.sum,
          current_month_cost: usage_stats["current_month"] || 0.0,
          previous_month_cost: usage_stats["previous_days"] || 0.0,
          budget_limit: tenant.ai_budget_limit || 500.0,
          total_requests: tenant.ai_usage_records.where(created_at: thirty_days_ago..Time.current).count
        }
      end
    end
  end

  ##
  # Get recent campaigns with minimal data for dashboard
  #
  # @param limit [Integer] Number of campaigns to return
  # @return [ActiveRecord::Relation] Recent campaigns
  #
  def recent_campaigns(limit = 5)
    tenant.campaigns
          .select(:id, :name, :status, :campaign_type, :budget_cents, :created_at, :updated_at)
          .includes(:email_campaign, :social_campaign, :seo_campaign)
          .order(updated_at: :desc)
          .limit(limit)
  end

  ##
  # Invalidate all caches for this tenant
  #
  def invalidate_caches
    Rails.cache.delete(stats_cache_key)
    Rails.cache.delete(platform_cache_key)
    Rails.cache.delete(performance_cache_key)
    Rails.cache.delete(ai_usage_cache_key)
  end

  ##
  # Warm up caches in background
  #
  def warm_caches
    # Trigger cache population in background job
    CacheWarmupJob.perform_later(tenant.id)
  end

  private

  def base_campaigns_query
    tenant.campaigns.order(created_at: :desc)
  end

  def apply_filters(campaigns, filters)
    campaigns = campaigns.where(status: filters[:status]) if filters[:status].present?
    campaigns = campaigns.where(campaign_type: filters[:type]) if filters[:type].present?

    if filters[:search].present?
      # Use full-text search if available, otherwise use ILIKE
      if ActiveRecord::Base.connection.adapter_name.downcase.include?("postgresql")
        campaigns = campaigns.where(
          "to_tsvector('english', coalesce(name, '') || ' ' || coalesce(description, '')) @@ plainto_tsquery('english', ?)",
          filters[:search]
        )
      else
        search_term = "%#{filters[:search]}%"
        campaigns = campaigns.where(
          "name ILIKE ? OR description ILIKE ?",
          search_term, search_term
        )
      end
    end

    campaigns
  end

  def process_campaign_statistics(stats)
    result = {
      total: 0,
      by_status: Hash.new(0),
      by_type: Hash.new(0),
      total_budget: 0.0,
      budget_by_status: Hash.new(0.0)
    }

    stats.each do |stat|
      count = stat.count.to_i
      budget = (stat.total_budget_cents.to_f / 100.0)

      result[:total] += count
      result[:by_status][stat.status] += count
      result[:by_type][stat.campaign_type] += count
      result[:total_budget] += budget
      result[:budget_by_status][stat.status] += budget
    end

    # Add convenience accessors
    result.merge(
      active: result[:by_status]["active"],
      draft: result[:by_status]["draft"],
      completed: result[:by_status]["completed"],
      paused: result[:by_status]["paused"]
    )
  end

  def performance_metrics_sql
    <<~SQL
      SELECT#{' '}
        COUNT(*) as total_campaigns,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_campaigns,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_campaigns,
        SUM(budget_cents) as total_budget,
        SUM(CASE WHEN status = 'active' THEN budget_cents ELSE 0 END) as active_budget,
        AVG(CASE#{' '}
          WHEN end_date IS NOT NULL AND start_date IS NOT NULL#{' '}
          THEN EXTRACT(DAY FROM end_date - start_date)#{' '}
          ELSE NULL#{' '}
        END) as avg_duration
      FROM campaigns#{' '}
      WHERE tenant_id = #{tenant.id}
    SQL
  end

  def calculate_success_rate(row)
    total = row["total_campaigns"].to_i
    completed = row["completed_campaigns"].to_i

    return 0.0 if total.zero?

    ((completed.to_f / total) * 100).round(1)
  end

  def default_performance_metrics
    {
      total_campaigns: 0,
      active_campaigns: 0,
      completed_campaigns: 0,
      total_budget: 0.0,
      active_budget: 0.0,
      avg_campaign_duration: 0.0,
      success_rate: 0.0
    }
  end

  # Cache key methods
  def stats_cache_key
    "campaign_stats_#{tenant.id}"
  end

  def platform_cache_key
    "platform_stats_#{tenant.id}"
  end

  def performance_cache_key
    "performance_metrics_#{tenant.id}"
  end

  def ai_usage_cache_key
    "ai_usage_stats_#{tenant.id}"
  end

  def logger
    Rails.logger
  end
end
