# frozen_string_literal: true

class CampaignMomentumService
  def initialize(tenant = nil)
    @tenant = tenant || ActsAsTenant.current_tenant
  end

  def real_time_momentum_data
    Rails.cache.fetch("momentum_#{@tenant.id}", expires_in: 1.minute) do
      {
        trending_campaigns: trending_campaigns_data,
        momentum_alerts: momentum_alerts,
        velocity_metrics: calculate_velocity_metrics,
        engagement_trends: recent_engagement_trends,
        performance_forecasts: generate_performance_forecasts
      }
    end
  end

  def trending_campaigns_data
    recent_metrics = CampaignMetric
                       .joins(:campaign)
                       .where(campaigns: { tenant: @tenant })
                       .recent(7)
                       .group(:campaign_id)
                       .select(
                         'campaign_id,
                          AVG(clicks::float / NULLIF(impressions, 0) * 100) as avg_ctr,
                          SUM(conversions) as total_conversions,
                          SUM(revenue_cents) as total_revenue,
                          COUNT(*) as data_points'
                       )

    trending_data = recent_metrics.map do |metric|
      campaign = Campaign.find(metric.campaign_id)
      momentum_score = calculate_momentum_score(campaign)

      {
        campaign_id: metric.campaign_id,
        campaign_name: campaign.name,
        momentum_score: momentum_score,
        trend_direction: determine_trend_direction(campaign),
        velocity: calculate_campaign_velocity(campaign),
        recent_performance: {
          avg_ctr: metric.avg_ctr&.round(2) || 0.0,
          total_conversions: metric.total_conversions || 0,
          total_revenue: (metric.total_revenue || 0) / 100.0
        }
      }
    end

    trending_data.sort_by { |c| -c[:momentum_score] }.first(10)
  end

  def momentum_alerts
    alerts = []

    # Declining performance alerts
    declining_campaigns = detect_declining_campaigns
    declining_campaigns.each do |campaign|
      alerts << {
        type: "declining_performance",
        severity: "warning",
        campaign_id: campaign[:id],
        campaign_name: campaign[:name],
        message: "Performance declining by #{campaign[:decline_rate]}% over last 24h",
        timestamp: Time.current
      }
    end

    # Breakout campaign alerts
    breakout_campaigns = detect_breakout_campaigns
    breakout_campaigns.each do |campaign|
      alerts << {
        type: "breakout_performance",
        severity: "info",
        campaign_id: campaign[:id],
        campaign_name: campaign[:name],
        message: "Exceptional growth detected: #{campaign[:growth_rate]}% increase",
        timestamp: Time.current
      }
    end

    # Budget depletion alerts
    budget_alerts = detect_budget_concerns
    budget_alerts.each do |alert|
      alerts << alert
    end

    alerts.sort_by { |a| a[:timestamp] }.reverse.first(20)
  end

  def calculate_velocity_metrics
    campaigns = Campaign.where(tenant: @tenant, status: [ :active, :paused ])

    total_velocity = 0
    velocity_data = campaigns.map do |campaign|
      velocity = calculate_campaign_velocity(campaign)
      total_velocity += velocity

      {
        campaign_id: campaign.id,
        name: campaign.name,
        velocity: velocity,
        acceleration: calculate_campaign_acceleration(campaign)
      }
    end

    {
      average_velocity: campaigns.count > 0 ? (total_velocity / campaigns.count).round(2) : 0.0,
      campaign_velocities: velocity_data.sort_by { |c| -c[:velocity] },
      velocity_distribution: calculate_velocity_distribution(velocity_data)
    }
  end

  def recent_engagement_trends
    begin
      metrics = CampaignMetric
                  .joins(:campaign)
                  .where(campaigns: { tenant: @tenant })
                  .where("campaign_metrics.created_at >= ?", 24.hours.ago)
                  .select("campaign_metrics.*")

      hourly_data = {}
      metrics.group_by { |m| m.created_at.beginning_of_hour }.each do |hour, hour_metrics|
        hourly_data[hour] = {
          "email_opens" => hour_metrics.sum(&:email_opens),
          "email_clicks" => hour_metrics.sum(&:email_clicks),
          "social_engagements" => hour_metrics.sum(&:social_engagements),
          "conversions" => hour_metrics.sum(&:conversions)
        }
      end

      {
        hourly_engagement: format_hourly_trends(hourly_data),
        peak_hours: identify_peak_engagement_hours(hourly_data),
        engagement_velocity: calculate_engagement_velocity(hourly_data)
      }
    rescue => e
      Rails.logger.error "Error calculating engagement trends: #{e.message}"
      {
        hourly_engagement: [],
        peak_hours: [],
        engagement_velocity: 0.0
      }
    end
  end

  def generate_performance_forecasts
    campaigns = Campaign.where(tenant: @tenant, status: :active)

    forecasts = campaigns.map do |campaign|
      historical_data = campaign.campaign_metrics.recent(14).order(:metric_date)
      next if historical_data.count < 3

      forecast = calculate_campaign_forecast(historical_data)

      {
        campaign_id: campaign.id,
        campaign_name: campaign.name,
        forecast_period: "7_days",
        predicted_metrics: forecast,
        confidence_level: calculate_forecast_confidence(historical_data)
      }
    end

    forecasts.compact
  end

  private

  def calculate_momentum_score(campaign)
    metrics = campaign.campaign_metrics.recent(7).order(:metric_date)
    return 0.0 if metrics.count < 2

    # Calculate weighted momentum based on multiple factors
    performance_trend = calculate_performance_trend(metrics)
    engagement_growth = calculate_engagement_growth(metrics)
    conversion_momentum = calculate_conversion_momentum(metrics)

    # Weighted scoring
    momentum = (performance_trend * 0.4) + (engagement_growth * 0.35) + (conversion_momentum * 0.25)
    [ momentum, 100.0 ].min.round(2)
  end

  def determine_trend_direction(campaign)
    recent_metrics = campaign.campaign_metrics.recent(3).order(:metric_date)
    return "stable" if recent_metrics.count < 2

    latest = recent_metrics.last
    previous = recent_metrics[-2]

    performance_change = calculate_performance_change(previous, latest)

    case performance_change
    when -Float::INFINITY..-5.0
      "declining"
    when -5.0..5.0
      "stable"
    else
      "growing"
    end
  end

  def calculate_campaign_velocity(campaign)
    metrics = campaign.campaign_metrics.recent(7).order(:metric_date)
    return 0.0 if metrics.count < 2

    # Calculate rate of change in key metrics
    first_metric = metrics.first
    last_metric = metrics.last
    days_diff = (last_metric.metric_date - first_metric.metric_date).to_f

    return 0.0 if days_diff == 0

    conversion_velocity = (last_metric.conversions - first_metric.conversions) / days_diff
    revenue_velocity = ((last_metric.revenue_cents - first_metric.revenue_cents) / 100.0) / days_diff

    # Normalized velocity score
    ((conversion_velocity * 10) + (revenue_velocity * 0.1)).round(2)
  end

  def calculate_campaign_acceleration(campaign)
    velocities = []
    metrics = campaign.campaign_metrics.recent(7).order(:metric_date)

    metrics.each_cons(2) do |prev, curr|
      days_diff = (curr.metric_date - prev.metric_date).to_f
      next if days_diff == 0

      velocity = (curr.conversions - prev.conversions) / days_diff
      velocities << velocity
    end

    return 0.0 if velocities.count < 2

    # Calculate acceleration as change in velocity
    acceleration = (velocities.last - velocities.first) / velocities.count
    acceleration.round(2)
  end

  def detect_declining_campaigns
    declining = []

    begin
      campaigns = Campaign.where(tenant: @tenant, status: [ :active, :paused ])

      campaigns.each do |campaign|
        recent_metrics = campaign.campaign_metrics.recent(2).order(:metric_date)
        next if recent_metrics.count < 2

        latest = recent_metrics.last
        previous = recent_metrics.first

        decline_rate = calculate_decline_rate(previous, latest)

        if decline_rate > 20 # 20% decline threshold
          declining << {
            id: campaign.id,
            name: campaign.name,
            decline_rate: decline_rate.round(1)
          }
        end
      end
    rescue => e
      Rails.logger.error "Error detecting declining campaigns: #{e.message}"
    end

    declining
  end

  def detect_breakout_campaigns
    campaigns = Campaign.where(tenant: @tenant, status: [ :active, :paused ])
    breakouts = []

    campaigns.each do |campaign|
      momentum_score = calculate_momentum_score(campaign)

      if momentum_score > 75 # High momentum threshold
        recent_growth = calculate_recent_growth_rate(campaign)

        breakouts << {
          id: campaign.id,
          name: campaign.name,
          growth_rate: recent_growth.round(1),
          momentum_score: momentum_score
        }
      end
    end

    breakouts
  end

  def detect_budget_concerns
    campaigns = Campaign.where(tenant: @tenant, status: :active)
    alerts = []

    campaigns.each do |campaign|
      next unless campaign.budget_cents.present? && campaign.budget_cents > 0

      total_spent = campaign.campaign_metrics.sum(:cost_cents)
      budget_utilization = (total_spent.to_f / campaign.budget_cents * 100)

      if budget_utilization > 90
        alerts << {
          type: "budget_depletion",
          severity: "critical",
          campaign_id: campaign.id,
          campaign_name: campaign.name,
          message: "Budget #{budget_utilization.round(1)}% depleted",
          timestamp: Time.current
        }
      elsif budget_utilization > 75
        alerts << {
          type: "budget_warning",
          severity: "warning",
          campaign_id: campaign.id,
          campaign_name: campaign.name,
          message: "Budget #{budget_utilization.round(1)}% depleted",
          timestamp: Time.current
        }
      end
    end

    alerts
  end

  def calculate_velocity_distribution(velocity_data)
    velocities = velocity_data.map { |c| c[:velocity] }

    {
      high_velocity: velocities.count { |v| v > 10 },
      medium_velocity: velocities.count { |v| v.between?(1, 10) },
      low_velocity: velocities.count { |v| v.between?(-1, 1) },
      declining: velocities.count { |v| v < -1 }
    }
  end

  def format_hourly_trends(hourly_data)
    hourly_data.map do |hour, metrics|
      {
        hour: hour,
        total_opens: metrics["email_opens"] || 0,
        total_clicks: metrics["email_clicks"] || 0,
        total_engagements: metrics["social_engagements"] || 0,
        total_conversions: metrics["conversions"] || 0
      }
    end.sort_by { |h| h[:hour] }
  end

  def identify_peak_engagement_hours(hourly_data)
    return [] if hourly_data.empty?

    total_engagements = hourly_data.map do |hour, metrics|
      total = (metrics["email_opens"] || 0) + (metrics["email_clicks"] || 0) + (metrics["social_engagements"] || 0)
      [ hour, total ]
    end

    sorted_hours = total_engagements.sort_by { |_, total| -total }
    sorted_hours.first(3).map { |hour, total| { hour: hour, engagement_count: total } }
  end

  def calculate_engagement_velocity(hourly_data)
    return 0.0 if hourly_data.count < 2

    sorted_data = hourly_data.sort_by { |hour, _| hour }
    first_hour = sorted_data.first[1]
    last_hour = sorted_data.last[1]

    first_total = (first_hour["email_opens"] || 0) + (first_hour["email_clicks"] || 0)
    last_total = (last_hour["email_opens"] || 0) + (last_hour["email_clicks"] || 0)

    hours_diff = sorted_data.count
    return 0.0 if hours_diff == 0

    ((last_total - first_total).to_f / hours_diff).round(2)
  end

  def calculate_campaign_forecast(historical_data)
    # Simple linear regression for trend forecasting
    metrics = historical_data.to_a
    days = metrics.map.with_index { |_, i| i + 1 }

    {
      predicted_conversions: forecast_metric(days, metrics.map(&:conversions)),
      predicted_revenue: forecast_metric(days, metrics.map { |m| m.revenue_cents / 100.0 }),
      predicted_ctr: forecast_metric(days, metrics.map { |m| m.impressions > 0 ? (m.clicks.to_f / m.impressions * 100) : 0 })
    }
  end

  def forecast_metric(days, values)
    return 0.0 if days.empty? || values.empty?

    n = days.length
    sum_x = days.sum
    sum_y = values.sum
    sum_xy = days.zip(values).map { |x, y| x * y }.sum
    sum_x2 = days.map { |x| x * x }.sum

    slope = (n * sum_xy - sum_x * sum_y).to_f / (n * sum_x2 - sum_x * sum_x)
    intercept = (sum_y - slope * sum_x).to_f / n

    # Forecast next 7 days
    next_value = slope * (n + 7) + intercept
    [ next_value, 0 ].max.round(2)
  end

  def calculate_forecast_confidence(historical_data)
    return 0.0 if historical_data.count < 3

    # Calculate variance in the data
    values = historical_data.map(&:conversions)
    mean = values.sum.to_f / values.length
    variance = values.sum { |v| (v - mean) ** 2 } / values.length
    coefficient_of_variation = variance > 0 ? Math.sqrt(variance) / mean : 0

    # Convert to confidence percentage (inverse relationship)
    confidence = [ 100 - (coefficient_of_variation * 100), 10 ].max
    confidence.round(2)
  end

  def calculate_performance_trend(metrics)
    return 0.0 if metrics.count < 2

    first = metrics.first
    last = metrics.last

    # Calculate percentage change in key performance indicators
    ctr_change = calculate_percentage_change(
      first.impressions > 0 ? (first.clicks.to_f / first.impressions) : 0,
      last.impressions > 0 ? (last.clicks.to_f / last.impressions) : 0
    )

    conversion_change = calculate_percentage_change(first.conversions, last.conversions)
    revenue_change = calculate_percentage_change(first.revenue_cents, last.revenue_cents)

    # Weighted average of performance changes
    (ctr_change * 0.4 + conversion_change * 0.35 + revenue_change * 0.25).round(2)
  end

  def calculate_engagement_growth(metrics)
    return 0.0 if metrics.count < 2

    first = metrics.first
    last = metrics.last

    email_growth = calculate_percentage_change(
      first.email_opens + first.email_clicks,
      last.email_opens + last.email_clicks
    )

    social_growth = calculate_percentage_change(first.social_engagements, last.social_engagements)

    # Average engagement growth
    ((email_growth + social_growth) / 2).round(2)
  end

  def calculate_conversion_momentum(metrics)
    return 0.0 if metrics.count < 2

    # Calculate acceleration in conversions
    conversion_values = metrics.map(&:conversions)
    velocities = []

    conversion_values.each_cons(2) do |prev, curr|
      velocities << (curr - prev)
    end

    return 0.0 if velocities.count < 2

    # Momentum as change in velocity
    momentum = velocities.last - velocities.first
    [ momentum * 10, 100 ].min
  end

  def calculate_performance_change(previous, latest)
    return 0.0 if previous.nil? || latest.nil?

    # Composite performance score
    prev_score = calculate_composite_score(previous)
    latest_score = calculate_composite_score(latest)

    calculate_percentage_change(prev_score, latest_score)
  end

  def calculate_composite_score(metric)
    ctr = metric.impressions > 0 ? (metric.clicks.to_f / metric.impressions * 100) : 0
    conversion_rate = metric.clicks > 0 ? (metric.conversions.to_f / metric.clicks * 100) : 0
    revenue = metric.revenue_cents / 100.0

    # Weighted composite score
    (ctr * 0.3) + (conversion_rate * 0.4) + (revenue * 0.001) # Revenue scaled down
  end

  def calculate_percentage_change(old_value, new_value)
    return 0.0 if old_value.nil? || new_value.nil? || old_value == 0

    ((new_value - old_value).to_f / old_value * 100).round(2)
  end

  def calculate_decline_rate(previous, latest)
    prev_score = calculate_composite_score(previous)
    latest_score = calculate_composite_score(latest)

    return 0.0 if prev_score == 0

    decline = ((prev_score - latest_score).to_f / prev_score * 100)
    [ decline, 0 ].max
  end

  def calculate_recent_growth_rate(campaign)
    metrics = campaign.campaign_metrics.recent(7).order(:metric_date)
    return 0.0 if metrics.count < 2

    first = metrics.first
    last = metrics.last

    calculate_percentage_change(calculate_composite_score(first), calculate_composite_score(last))
  end
end
