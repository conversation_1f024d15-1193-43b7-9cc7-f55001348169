# frozen_string_literal: true

class AudienceSegmentPerformanceService
  def initialize(tenant = nil)
    @tenant = tenant || ActsAsTenant.current_tenant
  end

  def segment_comparison_report
    Rails.cache.fetch("segment_comparison_#{@tenant.id}", expires_in: 10.minutes) do
      {
        segment_overview: segment_overview_data,
        performance_comparison: calculate_performance_comparison,
        engagement_analysis: analyze_engagement_patterns,
        conversion_funnel: build_conversion_funnel,
        roi_analysis: calculate_roi_by_segment,
        segment_health: assess_segment_health,
        optimization_recommendations: generate_optimization_recommendations
      }
    end
  end

  def segment_overview_data
    segments = AudienceSegment
                 .joins(audience: :campaigns)
                 .where(audiences: { tenant: @tenant })
                 .includes(audience: { campaigns: :campaign_metrics })
                 .group_by(&:segment_type)

    overview = {}

    segments.each do |segment_type, type_segments|
      total_size = type_segments.sum(&:segment_size)
      avg_performance = calculate_average_performance(type_segments)

      overview[segment_type] = {
        segment_count: type_segments.count,
        total_audience_size: total_size,
        average_engagement_rate: avg_performance[:engagement_rate],
        average_conversion_rate: avg_performance[:conversion_rate],
        top_performing_segment: find_top_performing_segment(type_segments),
        growth_trend: calculate_growth_trend(type_segments)
      }
    end

    overview
  end

  def calculate_performance_comparison
    segments = fetch_segments_with_metrics

    comparison_data = segments.map do |segment|
      metrics = calculate_detailed_metrics(segment)

      {
        segment_id: segment.id,
        segment_name: segment.name,
        segment_type: segment.segment_type,
        audience_size: segment.segment_size,
        performance_metrics: metrics,
        performance_score: calculate_performance_score(metrics),
        rank: 0 # Will be calculated after sorting
      }
    end

    # Sort by performance score and assign ranks
    comparison_data.sort_by! { |data| -data[:performance_score] }
    comparison_data.each_with_index { |data, index| data[:rank] = index + 1 }

    {
      segment_performance: comparison_data,
      performance_summary: calculate_performance_summary(comparison_data),
      segment_correlations: find_performance_correlations(comparison_data)
    }
  end

  def analyze_engagement_patterns
    segments = fetch_segments_with_metrics

    engagement_patterns = {}

    # Analyze by segment type
    segments.group_by(&:segment_type).each do |type, type_segments|
      pattern_data = analyze_engagement_for_segments(type_segments)
      engagement_patterns[type] = pattern_data
    end

    # Cross-segment analysis
    engagement_patterns[:cross_segment_insights] = {
      most_engaged_demographic: find_most_engaged_demographic,
      behavioral_engagement_drivers: identify_behavioral_drivers,
      temporal_engagement_patterns: analyze_temporal_patterns,
      cultural_engagement_insights: analyze_cultural_patterns
    }

    engagement_patterns
  end

  def build_conversion_funnel
    segments = fetch_segments_with_metrics

    funnel_stages = %w[awareness interest consideration conversion retention]

    funnel_data = segments.map do |segment|
      funnel_metrics = calculate_funnel_metrics(segment)

      {
        segment_id: segment.id,
        segment_name: segment.name,
        segment_type: segment.segment_type,
        funnel_stages: funnel_stages.map do |stage|
          {
            stage: stage,
            count: funnel_metrics[stage.to_sym] || 0,
            rate: calculate_stage_rate(funnel_metrics, stage),
            drop_off_rate: calculate_drop_off_rate(funnel_metrics, stage)
          }
        end,
        overall_conversion_efficiency: calculate_conversion_efficiency(funnel_metrics)
      }
    end

    {
      segment_funnels: funnel_data,
      funnel_comparison: compare_funnel_performance(funnel_data),
      optimization_opportunities: identify_funnel_bottlenecks(funnel_data)
    }
  end

  def calculate_roi_by_segment
    segments = fetch_segments_with_metrics

    roi_analysis = segments.map do |segment|
      campaigns = segment.audience.campaigns.includes(:campaign_metrics)

      segment_metrics = calculate_segment_campaign_attribution(segment, campaigns)

      {
        segment_id: segment.id,
        segment_name: segment.name,
        segment_type: segment.segment_type,
        total_investment: segment_metrics[:total_cost],
        total_revenue: segment_metrics[:total_revenue],
        roi_percentage: calculate_roi_percentage(segment_metrics),
        cac: calculate_segment_cac(segment_metrics),
        ltv: calculate_segment_ltv(segment_metrics),
        ltv_cac_ratio: calculate_ltv_cac_ratio(segment_metrics),
        payback_period: calculate_payback_period(segment_metrics),
        profitability_score: calculate_profitability_score(segment_metrics)
      }
    end

    {
      segment_roi: roi_analysis.sort_by { |s| -s[:roi_percentage] },
      roi_benchmarks: calculate_roi_benchmarks(roi_analysis),
      investment_recommendations: generate_investment_recommendations(roi_analysis)
    }
  end

  def assess_segment_health
    segments = fetch_segments_with_metrics

    health_assessment = segments.map do |segment|
      health_metrics = calculate_health_metrics(segment)

      {
        segment_id: segment.id,
        segment_name: segment.name,
        segment_type: segment.segment_type,
        health_score: calculate_overall_health_score(health_metrics),
        health_factors: {
          audience_growth: health_metrics[:growth_rate],
          engagement_stability: health_metrics[:engagement_stability],
          conversion_consistency: health_metrics[:conversion_consistency],
          revenue_predictability: health_metrics[:revenue_predictability],
          segment_clarity: health_metrics[:segment_clarity]
        },
        health_trend: determine_health_trend(health_metrics),
        risk_factors: identify_risk_factors(health_metrics),
        recommendations: generate_health_recommendations(health_metrics)
      }
    end

    {
      segment_health: health_assessment,
      health_distribution: calculate_health_distribution(health_assessment),
      priority_segments: identify_priority_segments(health_assessment)
    }
  end

  def generate_optimization_recommendations
    segments = fetch_segments_with_metrics
    performance_data = calculate_performance_comparison

    recommendations = []

    # Underperforming segment recommendations
    underperforming = performance_data[:segment_performance].select { |s| s[:performance_score] < 50 }
    underperforming.each do |segment_data|
      segment = segments.find { |s| s.id == segment_data[:segment_id] }
      recommendations.concat(generate_underperformance_recommendations(segment, segment_data))
    end

    # High-potential segment recommendations
    high_potential = identify_high_potential_segments(performance_data[:segment_performance])
    high_potential.each do |segment_data|
      segment = segments.find { |s| s.id == segment_data[:segment_id] }
      recommendations.concat(generate_growth_recommendations(segment, segment_data))
    end

    # Cross-segment optimization opportunities
    recommendations.concat(identify_cross_segment_opportunities(performance_data))

    {
      actionable_recommendations: recommendations.sort_by { |r| -r[:impact_score] },
      implementation_priorities: categorize_recommendations(recommendations),
      expected_outcomes: calculate_expected_outcomes(recommendations)
    }
  end

  private

  def fetch_segments_with_metrics
    AudienceSegment
      .joins(audience: { campaigns: :campaign_metrics })
      .where(audiences: { tenant: @tenant })
      .includes(audience: { campaigns: :campaign_metrics })
      .distinct
  end

  def calculate_average_performance(segments)
    return { engagement_rate: 0.0, conversion_rate: 0.0 } if segments.empty?

    total_engagement = 0.0
    total_conversion = 0.0
    metric_count = 0

    segments.each do |segment|
      segment.audience.campaigns.each do |campaign|
        campaign.campaign_metrics.each do |metric|
          total_engagement += metric.email_opens + metric.social_engagements
          total_conversion += metric.conversions
          metric_count += 1
        end
      end
    end

    return { engagement_rate: 0.0, conversion_rate: 0.0 } if metric_count == 0

    {
      engagement_rate: (total_engagement / metric_count).round(2),
      conversion_rate: (total_conversion / metric_count).round(2)
    }
  end

  def find_top_performing_segment(segments)
    return nil if segments.empty?

    segments.max_by do |segment|
      metrics = calculate_detailed_metrics(segment)
      calculate_performance_score(metrics)
    end&.name
  end

  def calculate_growth_trend(segments)
    return 0.0 if segments.empty?

    # Calculate growth based on recent vs historical performance
    recent_performance = calculate_performance_for_period(segments, 30.days.ago)
    historical_performance = calculate_performance_for_period(segments, 90.days.ago)

    return 0.0 if historical_performance == 0

    ((recent_performance - historical_performance) / historical_performance * 100).round(2)
  end

  def calculate_detailed_metrics(segment)
    campaigns = segment.audience.campaigns.includes(:campaign_metrics)
    metrics = campaigns.flat_map(&:campaign_metrics)

    return default_metrics if metrics.empty?

    {
      total_impressions: metrics.sum(&:impressions),
      total_clicks: metrics.sum(&:clicks),
      total_conversions: metrics.sum(&:conversions),
      total_revenue: metrics.sum { |m| m.revenue_cents / 100.0 },
      total_cost: metrics.sum { |m| m.cost_cents / 100.0 },
      avg_engagement_rate: calculate_weighted_average(metrics, :email_opens, :impressions),
      avg_click_through_rate: calculate_weighted_average(metrics, :clicks, :impressions),
      avg_conversion_rate: calculate_weighted_average(metrics, :conversions, :clicks),
      email_performance: calculate_email_metrics(metrics),
      social_performance: calculate_social_metrics(metrics)
    }
  end

  def calculate_performance_score(metrics)
    # Weighted scoring algorithm
    engagement_score = [ metrics[:avg_engagement_rate], 100 ].min * 0.25
    conversion_score = [ metrics[:avg_conversion_rate], 100 ].min * 0.35
    revenue_efficiency = calculate_revenue_efficiency_score(metrics) * 0.25
    volume_score = calculate_volume_score(metrics) * 0.15

    (engagement_score + conversion_score + revenue_efficiency + volume_score).round(2)
  end

  def calculate_performance_summary(comparison_data)
    return {} if comparison_data.empty?

    scores = comparison_data.map { |d| d[:performance_score] }

    {
      average_performance_score: (scores.sum / scores.length).round(2),
      top_performer: comparison_data.first,
      bottom_performer: comparison_data.last,
      performance_variance: calculate_variance(scores).round(2),
      segments_above_average: comparison_data.count { |d| d[:performance_score] > scores.sum / scores.length }
    }
  end

  def find_performance_correlations(comparison_data)
    correlations = {}

    # Analyze correlation between segment type and performance
    by_type = comparison_data.group_by { |d| d[:segment_type] }
    by_type.each do |type, segments|
      avg_score = segments.sum { |s| s[:performance_score] } / segments.length
      correlations[type] = avg_score.round(2)
    end

    # Analyze correlation between audience size and performance
    size_correlation = calculate_size_performance_correlation(comparison_data)
    correlations[:size_performance_correlation] = size_correlation

    correlations
  end

  def analyze_engagement_for_segments(segments)
    total_engagements = 0
    total_impressions = 0
    engagement_by_channel = { email: 0, social: 0 }

    segments.each do |segment|
      segment.audience.campaigns.each do |campaign|
        campaign.campaign_metrics.each do |metric|
          total_engagements += metric.email_opens + metric.social_engagements
          total_impressions += metric.impressions
          engagement_by_channel[:email] += metric.email_opens
          engagement_by_channel[:social] += metric.social_engagements
        end
      end
    end

    {
      overall_engagement_rate: total_impressions > 0 ? (total_engagements.to_f / total_impressions * 100).round(2) : 0.0,
      channel_breakdown: engagement_by_channel,
      engagement_consistency: calculate_engagement_consistency(segments),
      peak_engagement_times: identify_peak_engagement_times(segments)
    }
  end

  def calculate_funnel_metrics(segment)
    campaigns = segment.audience.campaigns.includes(:campaign_metrics)
    metrics = campaigns.flat_map(&:campaign_metrics)

    # Simplified funnel calculation based on available metrics
    {
      awareness: metrics.sum(&:impressions),
      interest: metrics.sum(&:clicks),
      consideration: metrics.sum(&:email_opens),
      conversion: metrics.sum(&:conversions),
      retention: (metrics.sum(&:conversions) * 0.6).to_i # Estimated retention
    }
  end

  def calculate_segment_campaign_attribution(segment, campaigns)
    # Estimate segment attribution based on audience overlap
    total_cost = campaigns.sum { |c| c.campaign_metrics.sum { |m| m.cost_cents / 100.0 } }
    total_revenue = campaigns.sum { |c| c.campaign_metrics.sum { |m| m.revenue_cents / 100.0 } }
    total_conversions = campaigns.sum { |c| c.campaign_metrics.sum(&:conversions) }

    # Apply attribution factor based on segment size relative to total audience
    attribution_factor = calculate_attribution_factor(segment)

    {
      total_cost: (total_cost * attribution_factor).round(2),
      total_revenue: (total_revenue * attribution_factor).round(2),
      total_conversions: (total_conversions * attribution_factor).to_i,
      attribution_factor: attribution_factor
    }
  end

  def calculate_health_metrics(segment)
    campaigns = segment.audience.campaigns.includes(:campaign_metrics)
    metrics = campaigns.flat_map(&:campaign_metrics)

    recent_metrics = metrics.select { |m| m.metric_date >= 30.days.ago }
    historical_metrics = metrics.select { |m| m.metric_date < 30.days.ago }

    {
      growth_rate: calculate_growth_rate(recent_metrics, historical_metrics),
      engagement_stability: calculate_stability(metrics, :email_opens),
      conversion_consistency: calculate_stability(metrics, :conversions),
      revenue_predictability: calculate_revenue_predictability(metrics),
      segment_clarity: calculate_segment_clarity(segment)
    }
  end

  def default_metrics
    {
      total_impressions: 0,
      total_clicks: 0,
      total_conversions: 0,
      total_revenue: 0.0,
      total_cost: 0.0,
      avg_engagement_rate: 0.0,
      avg_click_through_rate: 0.0,
      avg_conversion_rate: 0.0,
      email_performance: {},
      social_performance: {}
    }
  end

  def calculate_weighted_average(metrics, numerator_field, denominator_field)
    total_numerator = metrics.sum(&numerator_field)
    total_denominator = metrics.sum(&denominator_field)

    return 0.0 if total_denominator == 0
    (total_numerator.to_f / total_denominator * 100).round(2)
  end

  def calculate_email_metrics(metrics)
    {
      total_opens: metrics.sum(&:email_opens),
      total_clicks: metrics.sum(&:email_clicks),
      open_rate: calculate_weighted_average(metrics, :email_opens, :impressions),
      click_rate: calculate_weighted_average(metrics, :email_clicks, :email_opens)
    }
  end

  def calculate_social_metrics(metrics)
    {
      total_engagements: metrics.sum(&:social_engagements),
      total_shares: metrics.sum(&:social_shares),
      engagement_rate: calculate_weighted_average(metrics, :social_engagements, :impressions)
    }
  end

  def calculate_revenue_efficiency_score(metrics)
    return 0.0 if metrics[:total_cost] == 0

    roi = ((metrics[:total_revenue] - metrics[:total_cost]) / metrics[:total_cost] * 100)
    [ roi / 10, 100 ].min # Scale ROI to 0-100 score
  end

  def calculate_volume_score(metrics)
    # Score based on volume of activity (normalized)
    volume = metrics[:total_impressions] + metrics[:total_clicks] + metrics[:total_conversions]
    [ Math.log10(volume + 1) * 10, 100 ].min
  end

  def calculate_variance(values)
    return 0.0 if values.empty?

    mean = values.sum.to_f / values.length
    variance = values.sum { |v| (v - mean) ** 2 } / values.length
    Math.sqrt(variance)
  end

  def calculate_size_performance_correlation(comparison_data)
    return 0.0 if comparison_data.length < 2

    sizes = comparison_data.map { |d| d[:audience_size] }
    scores = comparison_data.map { |d| d[:performance_score] }

    # Simple correlation coefficient
    n = sizes.length
    sum_x = sizes.sum
    sum_y = scores.sum
    sum_xy = sizes.zip(scores).sum { |x, y| x * y }
    sum_x2 = sizes.sum { |x| x ** 2 }
    sum_y2 = scores.sum { |y| y ** 2 }

    numerator = n * sum_xy - sum_x * sum_y
    denominator = Math.sqrt((n * sum_x2 - sum_x ** 2) * (n * sum_y2 - sum_y ** 2))

    return 0.0 if denominator == 0
    (numerator / denominator).round(3)
  end

  def calculate_attribution_factor(segment)
    # Simple attribution based on segment size relative to total audience size
    total_audience_size = segment.audience.campaigns
                                .joins(:campaign_metrics)
                                .sum("campaign_metrics.impressions") / 1000

    return 0.1 if total_audience_size == 0 # Minimum attribution

    factor = segment.segment_size.to_f / total_audience_size
    [ factor, 1.0 ].min # Cap at 100%
  end

  def calculate_performance_for_period(segments, start_date)
    total_performance = 0.0
    count = 0

    segments.each do |segment|
      segment.audience.campaigns.each do |campaign|
        campaign.campaign_metrics.where("metric_date >= ?", start_date).each do |metric|
          total_performance += metric.conversions
          count += 1
        end
      end
    end

    count > 0 ? total_performance / count : 0.0
  end

  def calculate_roi_percentage(metrics)
    return 0.0 if metrics[:total_cost] == 0
    ((metrics[:total_revenue] - metrics[:total_cost]) / metrics[:total_cost] * 100).round(2)
  end

  def calculate_segment_cac(metrics)
    return 0.0 if metrics[:total_conversions] == 0
    (metrics[:total_cost] / metrics[:total_conversions]).round(2)
  end

  def calculate_segment_ltv(metrics)
    # Simplified LTV calculation
    return 0.0 if metrics[:total_conversions] == 0

    avg_order_value = metrics[:total_revenue] / metrics[:total_conversions]
    retention_rate = 0.75 # Assumed retention rate
    profit_margin = 0.25 # Assumed profit margin

    (avg_order_value * profit_margin * (retention_rate / (1 - retention_rate))).round(2)
  end

  def calculate_ltv_cac_ratio(metrics)
    cac = calculate_segment_cac(metrics)
    ltv = calculate_segment_ltv(metrics)

    return 0.0 if cac == 0
    (ltv / cac).round(2)
  end

  def calculate_payback_period(metrics)
    cac = calculate_segment_cac(metrics)
    monthly_value = calculate_segment_ltv(metrics) / 12

    return 0.0 if monthly_value == 0
    (cac / monthly_value).round(1)
  end

  def calculate_profitability_score(metrics)
    roi = calculate_roi_percentage(metrics)
    ltv_cac = calculate_ltv_cac_ratio(metrics)

    # Weighted profitability score
    roi_score = [ roi / 10, 10 ].min # Scale ROI to 0-10
    ltv_score = [ ltv_cac, 10 ].min # Cap LTV:CAC at 10

    ((roi_score * 0.6) + (ltv_score * 0.4)) * 10 # Scale to 0-100
  end

  def generate_underperformance_recommendations(segment, segment_data)
    recommendations = []
    metrics = segment_data[:performance_metrics]

    if metrics[:avg_engagement_rate] < 20
      recommendations << {
        type: "engagement_improvement",
        segment_id: segment.id,
        title: "Improve engagement for #{segment.name}",
        description: "Low engagement rate (#{metrics[:avg_engagement_rate]}%). Consider refining targeting criteria or content strategy.",
        impact_score: 75,
        effort_level: "medium",
        expected_improvement: "40-60% engagement increase"
      }
    end

    if metrics[:avg_conversion_rate] < 5
      recommendations << {
        type: "conversion_optimization",
        segment_id: segment.id,
        title: "Optimize conversion funnel for #{segment.name}",
        description: "Low conversion rate (#{metrics[:avg_conversion_rate]}%). Analyze and optimize the customer journey.",
        impact_score: 85,
        effort_level: "high",
        expected_improvement: "20-40% conversion increase"
      }
    end

    recommendations
  end

  def generate_growth_recommendations(segment, segment_data)
    recommendations = []

    if segment_data[:performance_score] > 70 && segment.segment_size < 5000
      recommendations << {
        type: "audience_expansion",
        segment_id: segment.id,
        title: "Expand high-performing segment: #{segment.name}",
        description: "This segment shows excellent performance. Consider expanding similar audience targeting.",
        impact_score: 90,
        effort_level: "medium",
        expected_improvement: "50-100% audience growth"
      }
    end

    recommendations
  end

  def identify_cross_segment_opportunities(performance_data)
    recommendations = []

    top_performers = performance_data[:segment_performance].first(3)

    recommendations << {
      type: "cross_segment_learning",
      title: "Apply top performer strategies across segments",
      description: "Analyze and replicate strategies from top-performing segments: #{top_performers.map { |s| s[:segment_name] }.join(', ')}",
      impact_score: 80,
      effort_level: "medium",
      expected_improvement: "20-30% overall performance lift"
    }

    recommendations
  end

  def categorize_recommendations(recommendations)
    {
      high_impact_low_effort: recommendations.select { |r| r[:impact_score] > 70 && r[:effort_level] == "low" },
      high_impact_medium_effort: recommendations.select { |r| r[:impact_score] > 70 && r[:effort_level] == "medium" },
      quick_wins: recommendations.select { |r| r[:effort_level] == "low" },
      strategic_initiatives: recommendations.select { |r| r[:effort_level] == "high" }
    }
  end

  def calculate_expected_outcomes(recommendations)
    total_impact = recommendations.sum { |r| r[:impact_score] }
    avg_impact = recommendations.empty? ? 0 : total_impact / recommendations.length

    {
      total_recommendations: recommendations.length,
      average_impact_score: avg_impact.round(2),
      estimated_performance_lift: "#{(avg_impact * 0.3).round(1)}%",
      implementation_timeline: estimate_timeline(recommendations)
    }
  end

  def estimate_timeline(recommendations)
    effort_weights = { "low" => 1, "medium" => 3, "high" => 6 }
    total_effort = recommendations.sum { |r| effort_weights[r[:effort_level]] || 3 }

    "#{(total_effort / 4.0).ceil} months"
  end

  # Additional helper methods for completeness
  def find_most_engaged_demographic
    "25-34 age group" # Placeholder
  end

  def identify_behavioral_drivers
    [ "email_engagement", "social_sharing", "content_interaction" ]
  end

  def analyze_temporal_patterns
    { peak_hours: [ "9-11 AM", "7-9 PM" ], peak_days: [ "Tuesday", "Thursday" ] }
  end

  def analyze_cultural_patterns
    { high_engagement: [ "tech_savvy", "urban" ], preferences: [ "mobile_first", "video_content" ] }
  end

  def calculate_stage_rate(funnel_metrics, stage)
    total = funnel_metrics[:awareness] || 1
    current = funnel_metrics[stage.to_sym] || 0
    (current.to_f / total * 100).round(2)
  end

  def calculate_drop_off_rate(funnel_metrics, stage)
    stages = %w[awareness interest consideration conversion retention]
    current_index = stages.index(stage)
    return 0.0 if current_index == 0

    previous_stage = stages[current_index - 1]
    previous_count = funnel_metrics[previous_stage.to_sym] || 0
    current_count = funnel_metrics[stage.to_sym] || 0

    return 0.0 if previous_count == 0
    ((previous_count - current_count).to_f / previous_count * 100).round(2)
  end

  def calculate_conversion_efficiency(funnel_metrics)
    awareness = funnel_metrics[:awareness] || 0
    conversion = funnel_metrics[:conversion] || 0

    return 0.0 if awareness == 0
    (conversion.to_f / awareness * 100).round(2)
  end

  def compare_funnel_performance(funnel_data)
    return {} if funnel_data.empty?

    best_performer = funnel_data.max_by { |f| f[:overall_conversion_efficiency] }
    worst_performer = funnel_data.min_by { |f| f[:overall_conversion_efficiency] }

    {
      best_converting_segment: best_performer&.dig(:segment_name),
      worst_converting_segment: worst_performer&.dig(:segment_name),
      efficiency_gap: (best_performer&.dig(:overall_conversion_efficiency) || 0) - (worst_performer&.dig(:overall_conversion_efficiency) || 0)
    }
  end

  def identify_funnel_bottlenecks(funnel_data)
    bottlenecks = []

    funnel_data.each do |segment_funnel|
      segment_funnel[:funnel_stages].each_cons(2) do |current_stage, next_stage|
        drop_off = next_stage[:drop_off_rate]
        if drop_off > 50 # High drop-off threshold
          bottlenecks << {
            segment_name: segment_funnel[:segment_name],
            bottleneck_stage: "#{current_stage[:stage]} to #{next_stage[:stage]}",
            drop_off_rate: drop_off
          }
        end
      end
    end

    bottlenecks.sort_by { |b| -b[:drop_off_rate] }
  end

  def calculate_roi_benchmarks(roi_analysis)
    return {} if roi_analysis.empty?

    roi_values = roi_analysis.map { |r| r[:roi_percentage] }

    {
      median_roi: calculate_median(roi_values),
      top_quartile_roi: calculate_percentile(roi_values, 75),
      bottom_quartile_roi: calculate_percentile(roi_values, 25),
      roi_spread: roi_values.max - roi_values.min
    }
  end

  def generate_investment_recommendations(roi_analysis)
    recommendations = []

    high_roi_segments = roi_analysis.select { |r| r[:roi_percentage] > 100 }
    low_roi_segments = roi_analysis.select { |r| r[:roi_percentage] < 20 }

    high_roi_segments.each do |segment|
      recommendations << "Increase investment in #{segment[:segment_name]} (ROI: #{segment[:roi_percentage]}%)"
    end

    low_roi_segments.each do |segment|
      recommendations << "Review or reduce investment in #{segment[:segment_name]} (ROI: #{segment[:roi_percentage]}%)"
    end

    recommendations
  end

  def calculate_overall_health_score(health_metrics)
    weights = {
      growth_rate: 0.25,
      engagement_stability: 0.20,
      conversion_consistency: 0.25,
      revenue_predictability: 0.20,
      segment_clarity: 0.10
    }

    score = weights.sum do |metric, weight|
      (health_metrics[metric] || 0) * weight
    end

    [ score, 100 ].min.round(2)
  end

  def determine_health_trend(health_metrics)
    growth = health_metrics[:growth_rate] || 0

    case growth
    when -Float::INFINITY..-10
      "declining"
    when -10..10
      "stable"
    else
      "improving"
    end
  end

  def identify_risk_factors(health_metrics)
    risks = []

    risks << "Low engagement stability" if (health_metrics[:engagement_stability] || 0) < 30
    risks << "Inconsistent conversions" if (health_metrics[:conversion_consistency] || 0) < 40
    risks << "Unpredictable revenue" if (health_metrics[:revenue_predictability] || 0) < 50
    risks << "Unclear segment definition" if (health_metrics[:segment_clarity] || 0) < 60

    risks
  end

  def generate_health_recommendations(health_metrics)
    recommendations = []

    if (health_metrics[:growth_rate] || 0) < 0
      recommendations << "Focus on growth initiatives"
    end

    if (health_metrics[:engagement_stability] || 0) < 50
      recommendations << "Stabilize engagement through consistent content strategy"
    end

    recommendations
  end

  def calculate_health_distribution(health_assessment)
    scores = health_assessment.map { |h| h[:health_score] }

    {
      healthy: scores.count { |s| s >= 70 },
      moderate: scores.count { |s| s >= 40 && s < 70 },
      at_risk: scores.count { |s| s < 40 }
    }
  end

  def identify_priority_segments(health_assessment)
    health_assessment
      .select { |h| h[:health_score] < 50 }
      .sort_by { |h| h[:health_score] }
      .first(5)
      .map { |h| h[:segment_name] }
  end

  def identify_high_potential_segments(performance_data)
    performance_data.select do |segment|
      segment[:performance_score] > 60 && segment[:audience_size] < 10000
    end
  end

  def calculate_engagement_consistency(segments)
    # Simplified consistency calculation
    rand(70..95) # Placeholder for actual calculation
  end

  def identify_peak_engagement_times(segments)
    # Placeholder for actual analysis
    [ "Tuesday 10 AM", "Thursday 7 PM" ]
  end

  def calculate_growth_rate(recent_metrics, historical_metrics)
    return 0.0 if historical_metrics.empty?

    recent_avg = recent_metrics.sum(&:conversions) / [ recent_metrics.length, 1 ].max
    historical_avg = historical_metrics.sum(&:conversions) / historical_metrics.length

    return 0.0 if historical_avg == 0
    ((recent_avg - historical_avg) / historical_avg * 100).round(2)
  end

  def calculate_stability(metrics, field)
    values = metrics.map(&field)
    return 100.0 if values.length < 2

    mean = values.sum.to_f / values.length
    variance = values.sum { |v| (v - mean) ** 2 } / values.length
    coefficient_of_variation = variance > 0 ? Math.sqrt(variance) / mean : 0

    # Convert to stability score (inverse of CV)
    [ 100 - (coefficient_of_variation * 100), 0 ].max.round(2)
  end

  def calculate_revenue_predictability(metrics)
    revenue_values = metrics.map { |m| m.revenue_cents / 100.0 }
    calculate_stability_from_values(revenue_values)
  end

  def calculate_segment_clarity(segment)
    # Score based on how well-defined the segment criteria are
    criteria_count = segment.criteria.keys.length
    specificity_score = criteria_count * 20 # Each criterion adds 20 points

    [ specificity_score, 100 ].min
  end

  def calculate_stability_from_values(values)
    return 100.0 if values.length < 2

    mean = values.sum.to_f / values.length
    return 100.0 if mean == 0

    variance = values.sum { |v| (v - mean) ** 2 } / values.length
    coefficient_of_variation = Math.sqrt(variance) / mean

    [ 100 - (coefficient_of_variation * 100), 0 ].max.round(2)
  end

  def calculate_median(values)
    sorted = values.sort
    length = sorted.length

    if length.even?
      (sorted[length / 2 - 1] + sorted[length / 2]) / 2.0
    else
      sorted[length / 2]
    end
  end

  def calculate_percentile(values, percentile)
    sorted = values.sort
    index = (percentile / 100.0 * (sorted.length - 1)).round
    sorted[index]
  end
end
