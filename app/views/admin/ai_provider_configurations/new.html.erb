<% content_for :title, "New AI Provider Configuration" %>

<div class="space-y-6">
  <!-- Header -->
  <div>
    <nav class="flex" aria-label="Breadcrumb">
      <ol role="list" class="flex items-center space-x-4">
        <li>
          <%= link_to admin_ai_provider_configurations_path, class: "text-gray-400 hover:text-gray-500" do %>
            <span>AI Provider Configurations</span>
          <% end %>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>
            <span class="ml-4 text-sm font-medium text-gray-500">New Configuration</span>
          </div>
        </li>
      </ol>
    </nav>
    <div class="mt-4">
      <h1 class="text-2xl font-bold text-gray-900">New AI Provider Configuration</h1>
      <p class="mt-2 text-sm text-gray-700">Add a new AI provider configuration to enable multi-provider support.</p>
    </div>
  </div>

  <!-- Form -->
  <div class="bg-white shadow sm:rounded-lg">
    <div class="px-4 py-5 sm:p-6">
      <%= form_with model: [:admin, @ai_provider_configuration], local: true, class: "space-y-6" do |form| %>
        <% if @ai_provider_configuration.errors.any? %>
          <div class="rounded-md bg-red-50 p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">
                  There were <%= pluralize(@ai_provider_configuration.errors.count, "error") %> with your submission:
                </h3>
                <div class="mt-2 text-sm text-red-700">
                  <ul role="list" class="list-disc list-inside">
                    <% @ai_provider_configuration.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
          <!-- Provider -->
          <div>
            <%= form.label :provider, class: "block text-sm font-medium text-gray-700" %>
            <%= form.select :provider, 
                options_for_select([
                  ['OpenAI', 'openai'],
                  ['Anthropic', 'anthropic'],
                  ['Google Gemini', 'gemini'],
                  ['DeepSeek', 'deepseek'],
                  ['Ollama', 'ollama'],
                  ['Other', 'other']
                ], @ai_provider_configuration.provider),
                { prompt: 'Select a provider' },
                { class: "mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md" } %>
          </div>

          <!-- Model Name -->
          <div>
            <%= form.label :model_name, class: "block text-sm font-medium text-gray-700" %>
            <%= form.text_field :model_name, 
                placeholder: "e.g., gpt-4, claude-3, gemini-pro",
                class: "mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" %>
          </div>

          <!-- API Key -->
          <div class="sm:col-span-2">
            <%= form.label :api_key, class: "block text-sm font-medium text-gray-700" %>
            <%= form.password_field :api_key, 
                placeholder: "Enter API key",
                class: "mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" %>
            <p class="mt-2 text-sm text-gray-500">API key will be encrypted and stored securely.</p>
          </div>

          <!-- Base URL -->
          <div class="sm:col-span-2">
            <%= form.label :base_url, class: "block text-sm font-medium text-gray-700" %>
            <%= form.url_field :base_url, 
                placeholder: "https://api.openai.com/v1 (optional)",
                class: "mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" %>
            <p class="mt-2 text-sm text-gray-500">Leave blank to use default provider endpoint.</p>
          </div>

          <!-- Max Tokens -->
          <div>
            <%= form.label :max_tokens, class: "block text-sm font-medium text-gray-700" %>
            <%= form.number_field :max_tokens, 
                placeholder: "4000",
                class: "mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" %>
          </div>

          <!-- Temperature -->
          <div>
            <%= form.label :temperature, class: "block text-sm font-medium text-gray-700" %>
            <%= form.number_field :temperature, 
                step: 0.1, min: 0, max: 2,
                placeholder: "0.7",
                class: "mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" %>
          </div>

          <!-- Cost per Token -->
          <div>
            <%= form.label :cost_per_token, class: "block text-sm font-medium text-gray-700" %>
            <%= form.number_field :cost_per_token, 
                step: 0.000001, min: 0,
                placeholder: "0.000002",
                class: "mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" %>
            <p class="mt-1 text-sm text-gray-500">Cost in USD per token</p>
          </div>

          <!-- Priority -->
          <div>
            <%= form.label :priority, class: "block text-sm font-medium text-gray-700" %>
            <%= form.number_field :priority, 
                min: 1, max: 10,
                placeholder: "5",
                class: "mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" %>
            <p class="mt-1 text-sm text-gray-500">1 = highest priority, 10 = lowest</p>
          </div>

          <!-- Task Types -->
          <div class="sm:col-span-2">
            <%= form.label :task_types, "Supported Task Types", class: "block text-sm font-medium text-gray-700" %>
            <div class="mt-2 space-y-2">
              <% %w[email_generation social_content blog_writing seo_optimization campaign_analysis].each do |task_type| %>
                <div class="flex items-center">
                  <%= check_box_tag "ai_provider_configuration[task_types][]", task_type, 
                      @ai_provider_configuration.task_types.include?(task_type),
                      id: "task_type_#{task_type}",
                      class: "focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded" %>
                  <%= label_tag "task_type_#{task_type}", task_type.humanize, 
                      class: "ml-3 text-sm font-medium text-gray-700" %>
                </div>
              <% end %>
            </div>
          </div>

          <!-- Is Active -->
          <div class="sm:col-span-2">
            <div class="flex items-center">
              <%= form.check_box :is_active, 
                  class: "focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded" %>
              <%= form.label :is_active, "Active", class: "ml-3 text-sm font-medium text-gray-700" %>
            </div>
            <p class="mt-2 text-sm text-gray-500">Only active configurations will be used for AI requests.</p>
          </div>
        </div>

        <!-- Submit buttons -->
        <div class="flex justify-end space-x-3">
          <%= link_to admin_ai_provider_configurations_path, 
              class: "bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
            Cancel
          <% end %>
          <%= form.submit "Create Configuration", 
              class: "ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
        </div>
      <% end %>
    </div>
  </div>
</div>
