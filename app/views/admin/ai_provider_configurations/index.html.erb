<% content_for :title, "AI Provider Configurations" %>

<div class="space-y-6">
  <!-- Header -->
  <div class="sm:flex sm:items-center sm:justify-between">
    <div>
      <h1 class="text-2xl font-bold text-gray-900">AI Provider Configurations</h1>
      <p class="mt-2 text-sm text-gray-700">Manage AI providers and their configurations for your organization.</p>
    </div>
    <%= link_to new_admin_ai_provider_configuration_path, 
        class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
      <svg class="-ml-1 mr-2 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd"/>
      </svg>
      Add Configuration
    <% end %>
  </div>

  <!-- Summary Cards -->
  <div class="grid grid-cols-1 gap-5 sm:grid-cols-3">
    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Active Providers</dt>
              <dd class="text-lg font-medium text-gray-900">
                <%= @providers_summary.select { |key, _| key.last == true }.values.sum %>
              </dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-gray-400 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z" clip-rule="evenodd"/>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Inactive Providers</dt>
              <dd class="text-lg font-medium text-gray-900">
                <%= @providers_summary.select { |key, _| key.last == false }.values.sum %>
              </dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Total Configurations</dt>
              <dd class="text-lg font-medium text-gray-900"><%= @ai_provider_configurations.count %></dd>
            </dl>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Configurations Table -->
  <div class="bg-white shadow overflow-hidden sm:rounded-md">
    <div class="px-4 py-5 sm:px-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900">Provider Configurations</h3>
      <p class="mt-1 max-w-2xl text-sm text-gray-500">Manage individual AI provider settings and credentials.</p>
    </div>

    <% if @ai_provider_configurations.any? %>
      <ul role="list" class="divide-y divide-gray-200">
        <% @ai_provider_configurations.each do |config| %>
          <li>
            <div class="px-4 py-4 flex items-center justify-between">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-10 h-10 bg-<%= config.is_active? ? 'green' : 'gray' %>-100 rounded-lg flex items-center justify-center">
                    <span class="text-<%= config.is_active? ? 'green' : 'gray' %>-600 font-semibold text-sm">
                      <%= config.provider.first(2).upcase %>
                    </span>
                  </div>
                </div>
                <div class="ml-4">
                  <div class="flex items-center">
                    <p class="text-sm font-medium text-gray-900">
                      <%= config.provider.titleize %> - <%= config.model_name %>
                    </p>
                    <% unless config.is_active? %>
                      <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        Inactive
                      </span>
                    <% end %>
                  </div>
                  <div class="flex items-center text-sm text-gray-500 space-x-4">
                    <span>Max Tokens: <%= number_with_delimiter(config.max_tokens) %></span>
                    <span>Cost: $<%= config.cost_per_token %>/token</span>
                    <span>Priority: <%= config.priority %></span>
                    <span>Tasks: <%= config.task_types.join(', ') if config.task_types.any? %></span>
                  </div>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <%= link_to admin_ai_provider_configuration_path(config), 
                    class: "text-indigo-600 hover:text-indigo-900 text-sm font-medium" do %>
                  View
                <% end %>
                <%= link_to edit_admin_ai_provider_configuration_path(config), 
                    class: "text-indigo-600 hover:text-indigo-900 text-sm font-medium" do %>
                  Edit
                <% end %>
                <%= link_to toggle_active_admin_ai_provider_configuration_path(config), 
                    method: :patch,
                    class: "text-#{config.is_active? ? 'red' : 'green'}-600 hover:text-#{config.is_active? ? 'red' : 'green'}-900 text-sm font-medium" do %>
                  <%= config.is_active? ? 'Deactivate' : 'Activate' %>
                <% end %>
              </div>
            </div>
          </li>
        <% end %>
      </ul>
    <% else %>
      <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No configurations</h3>
        <p class="mt-1 text-sm text-gray-500">Get started by creating your first AI provider configuration.</p>
        <div class="mt-6">
          <%= link_to new_admin_ai_provider_configuration_path, 
              class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
            <svg class="-ml-1 mr-2 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd"/>
            </svg>
            Add Configuration
          <% end %>
        </div>
      </div>
    <% end %>
  </div>
</div>
