<% content_for :title, "Reset Password" %>

<!-- Forgot Password Form -->
<div class="space-y-6">
  <!-- Header -->
  <div class="text-center">
    <h2 class="text-3xl font-bold text-gray-900">Reset your password</h2>
    <p class="mt-2 text-gray-600">Enter your email and we'll send you a reset link</p>
  </div>

  <!-- Reset Password Form -->
  <%= form_with(model: resource, as: resource_name, url: password_path(resource_name), local: true, class: "space-y-6") do |form| %>
    
    <!-- Email Field -->
    <div class="space-y-2">
      <%= form.label :email, class: "block text-sm font-medium text-gray-700" %>
      <div class="relative">
        <%= form.email_field :email, autofocus: true, autocomplete: "email",
            class: "block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 pl-12",
            placeholder: "Enter your email address" %>
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
          </svg>
        </div>
      </div>
    </div>

    <!-- Submit Button -->
    <%= form.submit "Send reset instructions", 
        class: "group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transform hover:-translate-y-0.5 transition-all duration-200 shadow-lg hover:shadow-xl" %>
  <% end %>

  <!-- Back to Sign In -->
  <div class="text-center">
    <%= link_to new_session_path(resource_name), 
        class: "group relative inline-flex items-center justify-center py-3 px-6 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200" do %>
      <svg class="mr-2 h-4 w-4 group-hover:-translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 17l-5-5m0 0l5-5m-5 5h12"></path>
      </svg>
      <span>Back to Sign In</span>
    <% end %>
  </div>
</div>