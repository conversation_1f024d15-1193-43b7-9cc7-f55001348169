<%# Platform Configuration Show - Individual Platform Management %>
<div class="bg-gray-50 min-h-screen">
  <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    
    <!-- Enhanced Header -->
    <div class="bg-white rounded-xl shadow-lg p-8 mb-8 bg-gradient-to-r from-blue-50 to-indigo-100"
         style="background: linear-gradient(135deg, #eff6ff 0%, #e0e7ff 100%);"">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <%= link_to platform_configurations_path, 
              class: "flex items-center text-gray-500 hover:text-blue-600 transition-colors duration-200 group" do %>
            <svg class="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
            <span class="font-medium">Back to Platforms</span>
          <% end %>
        </div>
        
        <div class="flex items-center space-x-3">
          <%= link_to refresh_token_platform_configuration_path(@platform_configuration.platform_name), 
              method: :post,
              class: "#{platform_button_classes(@platform_configuration.platform_name, 'inline-flex items-center px-4 py-2 shadow-sm text-sm font-semibold rounded-lg transition-all duration-200 transform hover:scale-105')}" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
            </svg>
            Refresh Token
          <% end %>
          
          <%= link_to platform_configuration_path(@platform_configuration.platform_name), 
              method: :delete,
              data: { 
                confirm: "Are you sure you want to disconnect #{@platform_configuration.platform_name.titleize}? This will stop all automated posting to this platform." 
              },
              class: "inline-flex items-center px-4 py-2 shadow-sm text-sm font-semibold rounded-lg text-red-700 bg-gradient-to-r from-red-50 to-red-100 hover:from-red-100 hover:to-red-200 transition-all duration-200 transform hover:scale-105" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
            Disconnect
          <% end %>
        </div>
      </div>
      
      <div class="mt-6">
        <div class="flex items-center space-x-6">            <div class="relative">
            <%= render "shared/platform_icons/#{@platform_configuration.platform_name}", 
                css_class: "w-16 h-16", 
                container_class: "platform-icon-container #{platform_background_classes(@platform_configuration.platform_name)}" %>
            <div class="absolute -top-2 -right-2 w-6 h-6 bg-emerald-500 rounded-full shadow-md flex items-center justify-center">
              <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 8 8">
                <path d="M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z"/>
              </svg>
            </div>
          </div>
          <div>
            <h1 class="text-3xl font-bold text-gray-900 flex items-center mb-2">
              <%= @platform_configuration.platform_name.titleize %>
              <svg class="w-6 h-6 ml-3 text-emerald-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </h1>
            <p class="text-gray-600 flex items-center">
              <svg class="w-4 h-4 mr-2 text-emerald-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
              </svg>
              Connected as <span class="font-semibold text-gray-900"><%= @platform_configuration.connected_account_name || 'Unknown' %></span>
            </p>
          </div>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Main Content -->
      <div class="lg:col-span-2 space-y-8">
        
        <!-- Connection Status -->
        <div class="bg-white rounded-xl shadow-sm p-6">
          <div class="flex items-center mb-6">
            <div class="p-2 rounded-lg bg-gradient-to-r from-emerald-50 to-emerald-100 mr-4">
              <svg class="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0"/>
              </svg>
            </div>
            <h2 class="text-xl font-bold text-gray-900">Connection Status</h2>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
              <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div class="flex items-center">
                  <svg class="w-4 h-4 text-emerald-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                  <span class="text-sm font-medium text-gray-700">Status</span>
                </div>
                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-emerald-100 text-emerald-800">
                  <div class="w-2 h-2 bg-emerald-400 rounded-full mr-2"></div>
                  Connected
                </span>
              </div>
              
              <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div class="flex items-center">
                  <svg class="w-4 h-4 text-slate-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V4a2 2 0 114 0v2m-4 0a2 2 0 104 0m-4 0V4a2 2 0 014 0v2"/>
                  </svg>
                  <span class="text-sm font-medium text-gray-700">Account ID</span>
                </div>
                <span class="text-sm text-gray-900 font-mono bg-white px-3 py-2 rounded-lg shadow-sm">
                  <%= @platform_configuration.connected_account_id || 'N/A' %>
                </span>
              </div>
              
              <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div class="flex items-center">
                  <svg class="w-4 h-4 text-slate-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                  <span class="text-sm font-medium text-gray-700">Last Sync</span>
                </div>
                <span class="text-sm text-gray-900 font-medium">
                  <%= @platform_configuration.last_sync_at ? time_ago_in_words(@platform_configuration.last_sync_at) + ' ago' : 'Never' %>
                </span>
              </div>
            </div>
            
            <div class="space-y-4">
              <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div class="flex items-center">
                  <svg class="w-4 h-4 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                  </svg>
                  <span class="text-sm font-medium text-gray-700">Auto Sync</span>
                </div>
                <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full <%= @platform_configuration.auto_sync_enabled? ? 'bg-emerald-100 text-emerald-800' : 'bg-slate-100 text-slate-800' %>">
                  <%= @platform_configuration.auto_sync_enabled? ? 'Enabled' : 'Disabled' %>
                </span>
              </div>
              
              <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div class="flex items-center">
                  <svg class="w-4 h-4 text-purple-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"/>
                  </svg>
                  <span class="text-sm font-medium text-gray-700">Posting</span>
                </div>
                <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full <%= @platform_configuration.posting_enabled? ? 'bg-emerald-100 text-emerald-800' : 'bg-slate-100 text-slate-800' %>">
                  <%= @platform_configuration.posting_enabled? ? 'Enabled' : 'Disabled' %>
                </span>
              </div>
              
              <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div class="flex items-center">
                  <svg class="w-4 h-4 text-indigo-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                  </svg>
                  <span class="text-sm font-medium text-gray-700">Analytics</span>
                </div>
                <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full <%= @platform_configuration.analytics_enabled? ? 'bg-emerald-100 text-emerald-800' : 'bg-slate-100 text-slate-800' %>">
                  <%= @platform_configuration.analytics_enabled? ? 'Enabled' : 'Disabled' %>
                </span>
              </div>
            </div>
          </div>          
          <% if @oauth_token %>
            <div class="mt-8 pt-6">
              <div class="w-full h-px bg-gradient-to-r from-transparent via-gray-200 to-transparent mb-6"></div>
              <div class="flex items-center mb-4">
                <div class="p-2 rounded-lg bg-gradient-to-r from-blue-50 to-blue-100 mr-3">
                  <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2a2 2 0 00-2 2m2-2V5a2 2 0 00-2-2H9a2 2 0 00-2 2v0"/>
                  </svg>
                </div>
                <h3 class="text-lg font-bold text-gray-900">OAuth Token Information</h3>
              </div>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div class="flex items-center">
                    <svg class="w-4 h-4 text-slate-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a.997.997 0 01-1.414 0l-7-7A1.997 1.997 0 013 12V7a4 4 0 014-4z"/>
                    </svg>
                    <span class="text-sm font-medium text-gray-700">Token Type</span>
                  </div>
                  <span class="text-sm text-gray-900 font-semibold"><%= @oauth_token.token_type %></span>
                </div>
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div class="flex items-center">
                    <svg class="w-4 h-4 text-slate-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4h6m-6 4h6m-7 4h8a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                    </svg>
                    <span class="text-sm font-medium text-gray-700">Expires</span>
                  </div>
                  <span class="text-sm text-gray-900 font-semibold">
                    <%= @oauth_token.expires_at ? @oauth_token.expires_at.strftime('%b %d, %Y') : 'Never' %>
                  </span>
                </div>
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div class="flex items-center">
                    <svg class="w-4 h-4 text-slate-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                    </svg>
                    <span class="text-sm font-medium text-gray-700">Scopes</span>
                  </div>
                  <span class="text-sm text-gray-900 font-semibold"><%= @oauth_token.scope || 'N/A' %></span>
                </div>
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div class="flex items-center">
                    <svg class="w-4 h-4 text-slate-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4h6m-6 4h6m-7 4h8a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                    </svg>
                    <span class="text-sm font-medium text-gray-700">Created</span>
                  </div>
                  <span class="text-sm text-gray-900 font-semibold"><%= @oauth_token.created_at.strftime('%b %d, %Y') %></span>
                </div>
              </div>
            </div>
          <% end %>
        </div>

        <!-- Recent Posts -->
        <div class="bg-white rounded-xl shadow-sm p-6">
          <div class="flex items-center justify-between mb-6">
            <div class="flex items-center">
              <div class="p-2 rounded-lg bg-gradient-to-r from-purple-50 to-purple-100 mr-4">
                <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 00-2 2v2a2 2 0 002 2m0 0h14m-14 0a2 2 0 002 2v2a2 2 0 01-2 2"/>
                </svg>
              </div>
              <h2 class="text-xl font-bold text-gray-900">Recent Posts</h2>
            </div>
            <div class="flex items-center text-sm text-gray-500">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              Last 30 days
            </div>
          </div>
          
          <% if @recent_posts.any? %>
            <div class="space-y-4">
              <% @recent_posts.each do |post| %>
                <div class="group bg-gradient-to-r from-gray-50 to-white rounded-xl p-4 hover:shadow-lg hover:from-blue-50 hover:to-indigo-50 transition-all duration-200">
                  <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                      <%= render "shared/platform_icons/#{@platform_configuration.platform_name}", 
                          css_class: "w-8 h-8", 
                          container_class: "platform-icon-container #{platform_background_classes(@platform_configuration.platform_name)} !p-2" %>
                    </div>
                    <div class="flex-1 min-w-0">
                      <p class="text-gray-900 text-sm leading-relaxed mb-3 line-clamp-3"><%= truncate(post[:content], length: 150) %></p>
                      <div class="flex items-center justify-between">
                        <div class="flex items-center text-xs text-gray-500">
                          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                          </svg>
                          <%= post[:created_at] %>
                        </div>
                        <div class="flex items-center space-x-4 text-xs">
                          <div class="flex items-center text-red-500 bg-red-50 px-2 py-1 rounded-full">
                            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 24 24">
                              <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                            </svg>
                            <%= post[:likes] || 0 %>
                          </div>
                          <div class="flex items-center text-blue-500 bg-blue-50 px-2 py-1 rounded-full">
                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                            </svg>
                            <%= post[:comments] || 0 %>
                          </div>
                          <div class="flex items-center text-green-500 bg-green-50 px-2 py-1 rounded-full">
                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"/>
                            </svg>
                            <%= post[:shares] || 0 %>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          <% else %>
            <div class="text-center py-12">
              <div class="flex justify-center mb-4">
                <%= render "shared/platform_icons/#{@platform_configuration.platform_name}",
                    css_class: "w-12 h-12", 
                    container_class: "platform-icon-container #{platform_background_classes(@platform_configuration.platform_name)} mx-auto opacity-30" %>
              </div>
              <div class="max-w-sm mx-auto">
                <h3 class="text-lg font-medium text-gray-900 mb-2">No recent posts found</h3>
                <p class="text-gray-500 text-sm">Posts will appear here once you start publishing content to <%= @platform_configuration.platform_name.titleize %>.</p>
              </div>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Sidebar -->
      <div class="space-y-8">
        
        <!-- Account Metrics -->
        <div class="bg-white rounded-xl shadow-sm p-6">
          <div class="flex items-center mb-4">
            <div class="w-1 h-6 <%= platform_background_classes(@platform_configuration.platform_name, 'rounded-full mr-3') %>"></div>
            <h3 class="text-lg font-semibold text-gray-900">Account Metrics</h3>
          </div>
          
          <% if @account_metrics.any? %>
            <div class="space-y-3">
              <% @account_metrics.each do |metric, value| %>
                <div class="flex justify-between items-center p-3 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg hover:from-blue-50 hover:to-indigo-50 transition-all duration-200">
                  <div class="flex items-center">
                    <div class="w-2 h-2 <%= platform_background_classes(@platform_configuration.platform_name, 'rounded-full mr-3') %>"></div>
                    <span class="text-sm font-medium text-gray-700"><%= metric.humanize %></span>
                  </div>
                  <span class="text-sm font-bold text-gray-900 px-2 py-1 bg-white rounded-lg shadow-sm">
                    <%= number_with_delimiter(value) %>
                  </span>
                </div>
              <% end %>
            </div>
          <% else %>
            <div class="text-center py-8">
              <div class="w-12 h-12 mx-auto mb-3 opacity-30">
                <%= render "shared/platform_icons/#{@platform_configuration.platform_name}",
                    css_class: "w-12 h-12", 
                    container_class: "platform-icon-container #{platform_background_classes(@platform_configuration.platform_name)}" %>
              </div>
              <p class="text-gray-500 text-sm">No metrics available</p>
            </div>
          <% end %>
        </div>

        <!-- Platform Settings -->
        <div class="bg-white rounded-xl shadow-sm p-6">
          <div class="flex items-center mb-4">
            <div class="w-1 h-6 <%= platform_background_classes(@platform_configuration.platform_name, 'rounded-full mr-3') %>"></div>
            <h3 class="text-lg font-semibold text-gray-900">Platform Settings</h3>
          </div>
          
          <%= form_with model: @platform_configuration, 
              url: platform_configuration_path(@platform_configuration.platform_name), 
              method: :patch, local: true do |form| %>
            
            <div class="space-y-4">
              <div class="flex items-center justify-between p-3 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg">
                <div class="flex items-center">
                  <svg class="w-4 h-4 text-blue-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                  </svg>
                  <%= form.label :auto_sync_enabled, "Auto Sync", class: "text-sm font-medium text-gray-700" %>
                </div>
                <%= form.check_box :auto_sync_enabled, 
                    class: "h-4 w-4 text-blue-600 focus:ring-blue-500 rounded transition-colors duration-200" %>
              </div>
              
              <div class="flex items-center justify-between p-3 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg">
                <div class="flex items-center">
                  <svg class="w-4 h-4 text-emerald-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"/>
                  </svg>
                  <%= form.label :posting_enabled, "Publishing", class: "text-sm font-medium text-gray-700" %>
                </div>
                <%= form.check_box :posting_enabled, 
                    class: "h-4 w-4 text-emerald-600 focus:ring-emerald-500 rounded transition-colors duration-200" %>
              </div>
              
              <div class="flex items-center justify-between p-3 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg">
                <div class="flex items-center">
                  <svg class="w-4 h-4 text-purple-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                  </svg>
                  <%= form.label :analytics_enabled, "Analytics", class: "text-sm font-medium text-gray-700" %>
                </div>
                <%= form.check_box :analytics_enabled, 
                    class: "h-4 w-4 text-purple-600 focus:ring-purple-500 rounded transition-colors duration-200" %>
              </div>
            </div>
            
            <div class="mt-6">
              <%= form.submit "Update Settings", 
                  class: "#{platform_button_classes(@platform_configuration.platform_name, 'w-full inline-flex justify-center items-center px-4 py-2 text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200')}" %>
            </div>
          <% end %>
        </div>

        <!-- Connection Health -->
        <div class="bg-white rounded-xl shadow-sm p-6">
          <div class="flex items-center mb-4">
            <div class="w-1 h-6 <%= platform_background_classes(@platform_configuration.platform_name, 'rounded-full mr-3') %>"></div>
            <h3 class="text-lg font-semibold text-gray-900">Connection Health</h3>
          </div>
          
          <button type="button" 
                  id="connectionTestBtn"
                  class="w-full inline-flex justify-center items-center px-4 py-2 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-gradient-to-r from-gray-50 to-gray-100 hover:from-gray-100 hover:to-gray-200 transition-all duration-200 transform hover:scale-105">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            Test Connection
          </button>
          
          <div id="connectionResult" class="mt-4 hidden">
            <!-- Results will be populated by JavaScript -->
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- JavaScript for Connection Testing -->
<script>
document.getElementById('connectionTestBtn').addEventListener('click', function() {
  const button = this;
  const resultDiv = document.getElementById('connectionResult');
  const originalContent = button.innerHTML;
  
  // Show loading state
  button.innerHTML = '<svg class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>Testing...';
  button.disabled = true;
  resultDiv.classList.add('hidden');
  
  fetch('<%= test_connection_platform_configuration_path(@platform_configuration.platform_name) %>', {
    method: 'GET',
    headers: {
      'Accept': 'application/json',
      'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      resultDiv.innerHTML = `
        <div class="p-3 bg-gradient-to-r from-emerald-50 to-emerald-100 rounded-lg shadow-sm">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-emerald-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span class="text-sm text-emerald-800 font-medium">Connection successful!</span>
          </div>
          ${data.account_info ? `<p class="text-xs text-emerald-700 mt-2">${data.account_info}</p>` : ''}
        </div>
      `;
    } else {
      resultDiv.innerHTML = `
        <div class="p-3 bg-gradient-to-r from-red-50 to-red-100 rounded-lg shadow-sm">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
            <span class="text-sm text-red-800 font-medium">Connection failed</span>
          </div>
          <p class="text-xs text-red-700 mt-2">${data.message}</p>
        </div>
      `;
    }
    
    resultDiv.classList.remove('hidden');
  })
  .catch(error => {
    console.error('Connection test error:', error);
    resultDiv.innerHTML = `
      <div class="p-3 bg-gradient-to-r from-red-50 to-red-100 rounded-lg shadow-sm">
        <div class="flex items-center">
          <svg class="w-5 h-5 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
          <span class="text-sm text-red-800 font-medium">Connection test failed</span>
        </div>
        <p class="text-xs text-red-700 mt-2">Please try again later.</p>
      </div>
    `;
    resultDiv.classList.remove('hidden');
  })
  .finally(() => {
    button.innerHTML = originalContent;
    button.disabled = false;
  });
});
</script>
