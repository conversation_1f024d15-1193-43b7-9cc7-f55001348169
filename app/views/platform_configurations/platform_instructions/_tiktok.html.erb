<div class="bg-gradient-to-r from-red-100 to-pink-100 rounded-lg p-4 mb-6">
  <div class="flex items-center mb-3">
    <svg class="w-5 h-5 text-red-600 mr-2" fill="currentColor" viewBox="0 0 24 24">
      <path d="M19.321 5.562a5.122 5.122 0 0 1-.443-.258 6.228 6.228 0 0 1-1.137-.966c-.849-.849-1.45-1.946-1.735-3.165h-.002c-.159-.68-.195-1.388-.107-2.073h.002c.044-.343.128-.679.25-.999H12.64v14.318c0 .696-.28 1.325-.734 1.779a2.5 2.5 0 0 1-1.779.734 2.507 2.507 0 0 1-2.513-2.513c0-.696.28-1.325.734-1.779a2.5 2.5 0 0 1 1.779-.734c.348 0 .679.07.98.197V8.53a5.55 5.55 0 0 0-.98-.088c-3.066 0-5.513 2.447-5.513 5.513s2.447 5.513 5.513 5.513 5.513-2.447 5.513-5.513V9.789a9.128 9.128 0 0 0 5.454 1.756V8.53a6.19 6.19 0 0 1-1.133-.178z"/>
    </svg>
    <h4 class="text-lg font-semibold text-red-900">TikTok Platform Setup</h4>
  </div>
  <p class="text-red-800 leading-relaxed">
    Connect your TikTok Business account to share engaging short-form videos and reach a younger audience.
  </p>
</div>

<div class="space-y-4">
  <div class="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow duration-200">
    <div class="flex items-start">
      <div class="flex-shrink-0 w-6 h-6 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">1</div>
      <div class="flex-1">
        <h5 class="font-semibold text-gray-900 mb-2">Access TikTok for Developers</h5>
        <p class="text-gray-700 text-sm mb-2">Create your developer account to access TikTok's APIs.</p>
        <a href="https://developers.tiktok.com" target="_blank" 
           class="inline-flex items-center text-red-600 hover:text-red-800 text-sm font-medium transition-colors duration-200">
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
          </svg>
          Open TikTok for Developers
        </a>
      </div>
    </div>
  </div>

  <div class="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow duration-200">
    <div class="flex items-start">
      <div class="flex-shrink-0 w-6 h-6 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">2</div>
      <div class="flex-1">
        <h5 class="font-semibold text-gray-900 mb-2">Create Your App</h5>
        <p class="text-gray-700 text-sm mb-3">Configure your TikTok application with these settings:</p>
        <div class="bg-gray-50 rounded-lg p-3 space-y-2">
          <div class="flex items-center">
            <svg class="w-4 h-4 text-gray-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <span class="text-sm"><strong>App Type:</strong> Web</span>
          </div>
          <div class="flex items-center">
            <svg class="w-4 h-4 text-gray-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <span class="text-sm"><strong>Platform:</strong> Web</span>
          </div>
          <div class="mt-3">
            <p class="text-sm font-medium text-gray-700 mb-1">Redirect URI:</p>
            <div class="bg-white rounded-md p-2 font-mono text-xs text-gray-800 break-all shadow-sm">
              <%= "#{request.base_url}/platform_configurations/oauth_callback/tiktok" %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow duration-200">
    <div class="flex items-start">
      <div class="flex-shrink-0 w-6 h-6 bg-amber-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">3</div>
      <div class="flex-1">
        <h5 class="font-semibold text-gray-900 mb-2">Request API Access</h5>
        <div class="bg-amber-50 rounded-lg p-3">
          <div class="flex items-start">
            <svg class="w-4 h-4 text-amber-600 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.865-.833-2.635 0L4.178 16.5c-.77.833.192 2.5 1.732 2.5z"/>
            </svg>
            <p class="text-amber-800 text-sm">
              Apply for access to the <strong>Content Posting API</strong> to enable video uploads.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow duration-200">
    <div class="flex items-start">
      <div class="flex-shrink-0 w-6 h-6 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">4</div>
      <div class="flex-1">
        <h5 class="font-semibold text-gray-900 mb-2">Get Your Credentials</h5>
        <p class="text-gray-700 text-sm">Copy your Client Key and Client Secret from the App Settings section.</p>
      </div>
    </div>
  </div>
</div>
