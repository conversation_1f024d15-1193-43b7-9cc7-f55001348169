<div class="bg-gradient-to-r from-pink-100 to-purple-100 rounded-lg p-4 mb-6">
  <div class="flex items-center mb-3">
    <svg class="w-5 h-5 text-pink-600 mr-2" fill="currentColor" viewBox="0 0 24 24">
      <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 2.982c.208-.008.281-.01 2.17-.01h.593c1.89 0 1.963.002 2.17.01 2.081.093 3.39 1.309 3.49 3.490.008.207.01.28.01 2.17v.593c0 1.89-.002 1.963-.01 2.17-.1 2.181-1.409 3.397-3.49 3.49-.207.008-.28.01-2.17.01h-.593c-1.89 0-1.963-.002-2.17-.01-2.081-.093-3.39-1.309-3.49-3.49-.008-.207-.01-.28-.01-2.17v-.593c0-1.89.002-1.963.01-2.17.1-2.181 1.409-3.397 3.49-3.49z"/>
    </svg>
    <h4 class="text-lg font-semibold text-pink-900">Instagram Platform Setup</h4>
  </div>
  <p class="text-pink-800 leading-relaxed">
    Connect your Instagram Business account to automatically publish photos, videos, and stories to your audience.
  </p>
</div>

<div class="space-y-4">
  <div class="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow duration-200">
    <div class="flex items-start">
      <div class="flex-shrink-0 w-6 h-6 bg-gradient-to-r from-pink-500 to-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">1</div>
      <div class="flex-1">
        <h5 class="font-semibold text-gray-900 mb-2">Access Facebook for Developers</h5>
        <p class="text-gray-700 text-sm mb-2">Instagram's API is managed through Facebook for Developers.</p>
        <a href="https://developers.facebook.com" target="_blank" 
           class="inline-flex items-center text-pink-600 hover:text-pink-800 text-sm font-medium transition-colors duration-200">
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
          </svg>
          Open Facebook for Developers
        </a>
      </div>
    </div>
  </div>

  <div class="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow duration-200">
    <div class="flex items-start">
      <div class="flex-shrink-0 w-6 h-6 bg-gradient-to-r from-pink-500 to-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">2</div>
      <div class="flex-1">
        <h5 class="font-semibold text-gray-900 mb-2">Configure Instagram Basic Display</h5>
        <p class="text-gray-700 text-sm mb-3">Set up your app with Instagram Basic Display API:</p>
        <div class="bg-gray-50 rounded-lg p-3 space-y-2">
          <div class="flex items-center">
            <svg class="w-4 h-4 text-gray-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <span class="text-sm"><strong>Product:</strong> Instagram Basic Display</span>
          </div>
          <div class="mt-3">
            <p class="text-sm font-medium text-gray-700 mb-1">Valid OAuth Redirect URIs:</p>
            <div class="bg-white rounded-md p-2 font-mono text-xs text-gray-800 break-all shadow-sm mb-2">
              <%= "#{request.base_url}/platform_configurations/oauth_callback/instagram" %>
            </div>
            <p class="text-sm font-medium text-gray-700 mb-1">Deauthorize Callback URL:</p>
            <div class="bg-white rounded-md p-2 font-mono text-xs text-gray-800 break-all shadow-sm">
              <%= "#{request.base_url}/webhooks/instagram/deauth" %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow duration-200">
    <div class="flex items-start">
      <div class="flex-shrink-0 w-6 h-6 bg-gradient-to-r from-pink-500 to-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">3</div>
      <div class="flex-1">
        <h5 class="font-semibold text-gray-900 mb-2">Get Your Credentials</h5>
        <p class="text-gray-700 text-sm">Copy your Instagram App ID and App Secret from the Instagram Basic Display settings.</p>
      </div>
    </div>
  </div>

  <div class="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow duration-200">
    <div class="flex items-start">
      <div class="flex-shrink-0 w-6 h-6 bg-gradient-to-r from-pink-500 to-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">4</div>
      <div class="flex-1">
        <h5 class="font-semibold text-gray-900 mb-2">Add Test Users</h5>
        <div class="bg-blue-50 rounded-lg p-3">
          <div class="flex items-start">
            <svg class="w-4 h-4 text-blue-600 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <p class="text-blue-800 text-sm">
              Add test users in the Roles section to test your integration before going live.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
