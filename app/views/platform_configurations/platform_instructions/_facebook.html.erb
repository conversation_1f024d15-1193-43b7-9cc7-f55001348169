<div class="bg-gradient-to-r from-blue-100 to-indigo-100 rounded-lg p-4 mb-6">
  <div class="flex items-center mb-3">
    <svg class="w-5 h-5 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 24 24">
      <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
    </svg>
    <h4 class="text-lg font-semibold text-blue-900">Facebook Platform Setup</h4>
  </div>
  <p class="text-blue-800 leading-relaxed">
    Connect your Facebook account to automatically publish posts and manage your business presence across Facebook and Instagram.
  </p>
</div>

<div class="space-y-4">
  <div class="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow duration-200">
    <div class="flex items-start">
      <div class="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">1</div>
      <div class="flex-1">
        <h5 class="font-semibold text-gray-900 mb-2">Create Developer Account</h5>
        <p class="text-gray-700 text-sm mb-2">Visit Facebook for Developers to set up your account.</p>
        <a href="https://developers.facebook.com" target="_blank" 
           class="inline-flex items-center text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors duration-200">
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
          </svg>
          Open Facebook for Developers
        </a>
      </div>
    </div>
  </div>

  <div class="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow duration-200">
    <div class="flex items-start">
      <div class="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">2</div>
      <div class="flex-1">
        <h5 class="font-semibold text-gray-900 mb-2">Configure Your App</h5>
        <p class="text-gray-700 text-sm mb-3">Create a new app with the following configuration:</p>
        <div class="bg-gray-50 rounded-lg p-3 space-y-2">
          <div class="flex items-center">
            <svg class="w-4 h-4 text-gray-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <span class="text-sm"><strong>App Type:</strong> Business</span>
          </div>
          <div class="flex items-center">
            <svg class="w-4 h-4 text-gray-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <span class="text-sm"><strong>Products:</strong> Facebook Login + Pages API</span>
          </div>
          <div class="mt-3">
            <p class="text-sm font-medium text-gray-700 mb-1">OAuth Redirect URI:</p>
            <div class="bg-white rounded-md p-2 font-mono text-xs text-gray-800 break-all shadow-sm">
              <%= "#{request.base_url}/platform_configurations/oauth_callback/facebook" %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow duration-200">
    <div class="flex items-start">
      <div class="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">3</div>
      <div class="flex-1">
        <h5 class="font-semibold text-gray-900 mb-2">Get Your Credentials</h5>
        <p class="text-gray-700 text-sm">Copy your App ID and App Secret from the Basic Settings section of your Facebook app.</p>
      </div>
    </div>
  </div>

  <div class="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow duration-200">
    <div class="flex items-start">
      <div class="flex-shrink-0 w-6 h-6 bg-amber-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">4</div>
      <div class="flex-1">
        <h5 class="font-semibold text-gray-900 mb-2">App Review (Important)</h5>
        <div class="bg-amber-50 rounded-lg p-3">
          <div class="flex items-start">
            <svg class="w-4 h-4 text-amber-600 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.865-.833-2.635 0L4.178 16.5c-.77.833.192 2.5 1.732 2.5z"/>
            </svg>
            <p class="text-amber-800 text-sm">
              Submit your app for review to access publishing permissions. This process may take several days.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
