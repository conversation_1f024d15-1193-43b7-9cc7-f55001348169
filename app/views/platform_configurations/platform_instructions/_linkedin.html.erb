<div class="bg-gradient-to-r from-blue-100 to-cyan-100 rounded-lg p-4 mb-6">
  <div class="flex items-center mb-3">
    <svg class="w-5 h-5 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 24 24">
      <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
    </svg>
    <h4 class="text-lg font-semibold text-blue-900">LinkedIn Platform Setup</h4>
  </div>
  <p class="text-blue-800 leading-relaxed">
    Connect your LinkedIn company page to share professional content and reach your business network.
  </p>
</div>

<div class="space-y-4">
  <div class="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow duration-200">
    <div class="flex items-start">
      <div class="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">1</div>
      <div class="flex-1">
        <h5 class="font-semibold text-gray-900 mb-2">Access Developer Portal</h5>
        <p class="text-gray-700 text-sm mb-2">Start by creating your LinkedIn developer application.</p>
        <a href="https://www.linkedin.com/developers" target="_blank" 
           class="inline-flex items-center text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors duration-200">
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
          </svg>
          Open LinkedIn Developer Portal
        </a>
      </div>
    </div>
  </div>

  <div class="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow duration-200">
    <div class="flex items-start">
      <div class="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">2</div>
      <div class="flex-1">
        <h5 class="font-semibold text-gray-900 mb-2">Create Your Application</h5>
        <p class="text-gray-700 text-sm mb-3">Set up your LinkedIn app with the following requirements:</p>
        <div class="bg-gray-50 rounded-lg p-3 space-y-2">
          <div class="flex items-center">
            <svg class="w-4 h-4 text-gray-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <span class="text-sm"><strong>App Name:</strong> Choose a descriptive name</span>
          </div>
          <div class="flex items-center">
            <svg class="w-4 h-4 text-gray-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <span class="text-sm"><strong>LinkedIn Page:</strong> Associate with your company page</span>
          </div>
          <div class="mt-3">
            <p class="text-sm font-medium text-gray-700 mb-1">Redirect URLs:</p>
            <div class="bg-white rounded-md p-2 font-mono text-xs text-gray-800 break-all shadow-sm">
              <%= "#{request.base_url}/platform_configurations/oauth_callback/linkedin" %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow duration-200">
    <div class="flex items-start">
      <div class="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">3</div>
      <div class="flex-1">
        <h5 class="font-semibold text-gray-900 mb-2">Request Product Access</h5>
        <div class="bg-amber-50 rounded-lg p-3">
          <div class="flex items-start">
            <svg class="w-4 h-4 text-amber-600 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.865-.833-2.635 0L4.178 16.5c-.77.833.192 2.5 1.732 2.5z"/>
            </svg>
            <p class="text-amber-800 text-sm">
              Request access to the <strong>"Share on LinkedIn"</strong> product for posting capabilities.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow duration-200">
    <div class="flex items-start">
      <div class="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">4</div>
      <div class="flex-1">
        <h5 class="font-semibold text-gray-900 mb-2">Get Your Credentials</h5>
        <p class="text-gray-700 text-sm">Copy your Client ID and Client Secret from the Auth tab of your LinkedIn application.</p>
      </div>
    </div>
  </div>
</div>
