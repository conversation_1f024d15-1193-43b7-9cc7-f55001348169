<div class="bg-gradient-to-r from-sky-100 to-blue-100 rounded-lg p-4 mb-6">
  <div class="flex items-center mb-3">
    <svg class="w-5 h-5 text-sky-600 mr-2" fill="currentColor" viewBox="0 0 24 24">
      <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
    </svg>
    <h4 class="text-lg font-semibold text-sky-900">Twitter/X Platform Setup</h4>
  </div>
  <p class="text-sky-800 leading-relaxed">
    Connect your Twitter/X account to share real-time updates, engage with your audience, and build your brand presence.
  </p>
</div>

<div class="space-y-4">
  <div class="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow duration-200">
    <div class="flex items-start">
      <div class="flex-shrink-0 w-6 h-6 bg-sky-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">1</div>
      <div class="flex-1">
        <h5 class="font-semibold text-gray-900 mb-2">Access Developer Portal</h5>
        <p class="text-gray-700 text-sm mb-2">Sign up for a Twitter Developer account to access the API.</p>
        <a href="https://developer.twitter.com" target="_blank" 
           class="inline-flex items-center text-sky-600 hover:text-sky-800 text-sm font-medium transition-colors duration-200">
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
          </svg>
          Open Twitter Developer Portal
        </a>
      </div>
    </div>
  </div>

  <div class="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow duration-200">
    <div class="flex items-start">
      <div class="flex-shrink-0 w-6 h-6 bg-sky-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">2</div>
      <div class="flex-1">
        <h5 class="font-semibold text-gray-900 mb-2">Create Your Application</h5>
        <p class="text-gray-700 text-sm mb-3">Set up your Twitter app with these essential settings:</p>
        <div class="bg-gray-50 rounded-lg p-3 space-y-2">
          <div class="flex items-center">
            <svg class="w-4 h-4 text-gray-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <span class="text-sm"><strong>App Permissions:</strong> Read and Write</span>
          </div>
          <div class="flex items-center">
            <svg class="w-4 h-4 text-gray-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <span class="text-sm"><strong>Type of App:</strong> Web App</span>
          </div>
          <div class="mt-3">
            <p class="text-sm font-medium text-gray-700 mb-1">Callback URL:</p>
            <div class="bg-white rounded-md p-2 font-mono text-xs text-gray-800 break-all shadow-sm">
              <%= "#{request.base_url}/platform_configurations/oauth_callback/twitter" %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow duration-200">
    <div class="flex items-start">
      <div class="flex-shrink-0 w-6 h-6 bg-sky-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">3</div>
      <div class="flex-1">
        <h5 class="font-semibold text-gray-900 mb-2">Get Your Credentials</h5>
        <p class="text-gray-700 text-sm">Copy your Client ID and Client Secret from the app settings in your Twitter Developer dashboard.</p>
      </div>
    </div>
  </div>

  <div class="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow duration-200">
    <div class="flex items-start">
      <div class="flex-shrink-0 w-6 h-6 bg-sky-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">4</div>
      <div class="flex-1">
        <h5 class="font-semibold text-gray-900 mb-2">Enable OAuth 2.0</h5>
        <div class="bg-blue-50 rounded-lg p-3">
          <div class="flex items-start">
            <svg class="w-4 h-4 text-blue-600 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <p class="text-blue-800 text-sm">
              Make sure to enable OAuth 2.0 authentication in your app settings for secure connections.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
