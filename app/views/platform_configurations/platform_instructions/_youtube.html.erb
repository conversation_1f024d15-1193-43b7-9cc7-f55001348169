<div class="bg-gradient-to-r from-red-100 to-orange-100 rounded-lg p-4 mb-6">
  <div class="flex items-center mb-3">
    <svg class="w-5 h-5 text-red-600 mr-2" fill="currentColor" viewBox="0 0 24 24">
      <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
    </svg>
    <h4 class="text-lg font-semibold text-red-900">YouTube Platform Setup</h4>
  </div>
  <p class="text-red-800 leading-relaxed">
    Connect your YouTube channel to upload videos, manage content, and grow your video marketing presence.
  </p>
</div>

<div class="space-y-4">
  <div class="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow duration-200">
    <div class="flex items-start">
      <div class="flex-shrink-0 w-6 h-6 bg-red-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">1</div>
      <div class="flex-1">
        <h5 class="font-semibold text-gray-900 mb-2">Access Google Cloud Console</h5>
        <p class="text-gray-700 text-sm mb-2">Create a Google Cloud Platform project to access YouTube APIs.</p>
        <a href="https://console.cloud.google.com" target="_blank" 
           class="inline-flex items-center text-red-600 hover:text-red-800 text-sm font-medium transition-colors duration-200">
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
          </svg>
          Open Google Cloud Console
        </a>
      </div>
    </div>
  </div>

  <div class="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow duration-200">
    <div class="flex items-start">
      <div class="flex-shrink-0 w-6 h-6 bg-red-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">2</div>
      <div class="flex-1">
        <h5 class="font-semibold text-gray-900 mb-2">Enable YouTube API</h5>
        <p class="text-gray-700 text-sm mb-3">Set up your project with YouTube Data API access:</p>
        <div class="bg-gray-50 rounded-lg p-3 space-y-2">
          <div class="flex items-center">
            <svg class="w-4 h-4 text-gray-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <span class="text-sm"><strong>API:</strong> YouTube Data API v3</span>
          </div>
          <div class="flex items-center">
            <svg class="w-4 h-4 text-gray-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <span class="text-sm"><strong>Credentials:</strong> OAuth 2.0 Client IDs</span>
          </div>
          <div class="mt-3">
            <p class="text-sm font-medium text-gray-700 mb-1">Authorized Redirect URIs:</p>
            <div class="bg-white rounded-md p-2 font-mono text-xs text-gray-800 break-all shadow-sm">
              <%= "#{request.base_url}/platform_configurations/oauth_callback/youtube" %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow duration-200">
    <div class="flex items-start">
      <div class="flex-shrink-0 w-6 h-6 bg-amber-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">3</div>
      <div class="flex-1">
        <h5 class="font-semibold text-gray-900 mb-2">Configure OAuth Consent</h5>
        <div class="bg-amber-50 rounded-lg p-3">
          <div class="flex items-start">
            <svg class="w-4 h-4 text-amber-600 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.865-.833-2.635 0L4.178 16.5c-.77.833.192 2.5 1.732 2.5z"/>
            </svg>
            <p class="text-amber-800 text-sm">
              Set up the OAuth consent screen with your application information and required scopes for YouTube access.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow duration-200">
    <div class="flex items-start">
      <div class="flex-shrink-0 w-6 h-6 bg-red-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">4</div>
      <div class="flex-1">
        <h5 class="font-semibold text-gray-900 mb-2">Get Your Credentials</h5>
        <p class="text-gray-700 text-sm">Download your Client ID and Client Secret from the Credentials page in Google Cloud Console.</p>
      </div>
    </div>
  </div>
</div>
