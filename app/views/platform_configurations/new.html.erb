<%# New Platform Configuration Form %>
<div class="bg-gray-50 min-h-screen">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    
    <!-- Enhanced Header -->
    <div class="bg-white rounded-xl shadow-sm p-8 mb-8 border-l-4 border-blue-500">
      <div class="flex items-center justify-between mb-6">
        <div class="flex items-center space-x-4">
          <div class="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl shadow-lg">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
            </svg>
          </div>
          <%= link_to platform_configurations_path, 
              class: "flex items-center text-gray-500 hover:text-blue-600 transition-colors duration-200 group" do %>
            <svg class="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
            <span class="font-medium">Back to Platforms</span>
          <% end %>
        </div>
      </div>
      <div class="flex items-center space-x-6">
        <div class="relative">
          <%= render "shared/platform_icons/#{@platform_name}", css_class: "w-16 h-16" %>
          <div class="absolute -top-2 -right-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
            </svg>
          </div>
        </div>
        <div>
          <h1 class="text-3xl font-bold text-gray-900 flex items-center mb-2">
            Connect <%= @platform_name.titleize %>
            <svg class="w-6 h-6 ml-3 text-emerald-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
            </svg>
          </h1>
          <p class="text-gray-600 flex items-center">
            <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
            </svg>
            Set up OAuth integration to publish content automatically
          </p>
        </div>
      </div>
    </div>

    <!-- Configuration Form -->
    <div class="bg-white rounded-xl shadow-sm">
      <%= form_with model: @platform_configuration, url: platform_configurations_path, local: true, 
          html: { class: "p-8 space-y-8" } do |form| %>
        
        <%= form.hidden_field :platform_name, value: @platform_name %>
        
        <!-- Enhanced Platform-Specific Instructions -->
        <div class="bg-gradient-to-r from-blue-100 to-indigo-100 rounded-xl p-6 shadow-sm">
          <div class="flex items-start space-x-4">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
            </div>
            <div class="flex-1">
              <div class="flex items-center space-x-2 mb-3">
                <h3 class="text-lg font-semibold text-blue-900">Before you begin - <%= @platform_name.titleize %> Setup</h3>
                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                </svg>
              </div>
              
              <%= render partial: "platform_configurations/platform_instructions/#{@platform_name}" %>
            </div>
          </div>
        </div>

        <!-- Enhanced OAuth Credentials Section -->
        <div class="border-b border-gray-200 pb-8">
          <div class="flex items-center space-x-3 mb-6">
            <div class="w-10 h-10 bg-gradient-to-br from-emerald-500 to-green-600 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"/>
              </svg>
            </div>
            <div>
              <h2 class="text-xl font-bold text-gray-900">OAuth Credentials</h2>
              <p class="text-gray-600">Enter the API credentials from your <%= @platform_name.titleize %> developer application.</p>
            </div>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-2">
              <%= form.label :client_id, class: "flex items-center text-sm font-semibold text-gray-700" do %>
                <svg class="w-4 h-4 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
                </svg>
                Client ID
              <% end %>
              <%= form.text_field :client_id, 
                  class: "w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",
                  placeholder: "Enter your Client ID",
                  required: true %>
              <% if form.object.errors[:client_id].any? %>
                <p class="mt-2 text-sm text-red-600 flex items-center">
                  <svg class="w-4 h-4 mr-1 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                  <%= form.object.errors[:client_id].first %>
                </p>
              <% end %>
            </div>
            
            <div class="space-y-2">
              <%= form.label :client_secret, class: "flex items-center text-sm font-semibold text-gray-700" do %>
                <svg class="w-4 h-4 mr-2 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                </svg>
                Client Secret
              <% end %>
              <%= form.password_field :client_secret, 
                  class: "w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",
                  placeholder: "Enter your Client Secret",
                  required: true %>
              <% if form.object.errors[:client_secret].any? %>
                <p class="mt-2 text-sm text-red-600 flex items-center">
                  <svg class="w-4 h-4 mr-1 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                  <%= form.object.errors[:client_secret].first %>
                </p>
              <% end %>
            </div>
          </div>
        </div>

        <!-- Enhanced Webhook Configuration -->
        <div class="border-b border-gray-200 pb-8">
          <div class="flex items-center space-x-3 mb-6">
            <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V4a2 2 0 10-4 0v1.341C7.67 7.165 6 9.388 6 12v2.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"/>
              </svg>
            </div>
            <div>
              <h2 class="text-xl font-bold text-gray-900">Webhook Configuration</h2>
              <p class="text-gray-600">Configure webhook URL for real-time updates (optional but recommended).</p>
            </div>
          </div>
          <div class="bg-gradient-to-r from-indigo-100 to-blue-100 rounded-lg p-6">
            <%= form.label :webhook_url, class: "flex items-center text-sm font-semibold text-gray-700 mb-3" do %>
              <svg class="w-4 h-4 mr-2 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
              </svg>
              Webhook URL
            <% end %>
            <%= form.url_field :webhook_url, 
                class: "w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors bg-white",
                placeholder: "https://your-domain.com/webhooks/#{@platform_name}",
                value: "#{request.base_url}/webhooks/#{@platform_name}" %>
            <div class="mt-3 flex items-start space-x-2">
              <svg class="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              <p class="text-sm text-gray-600">
                This URL will receive real-time updates from <%= @platform_name.titleize %>. 
                Copy this URL to your platform's webhook configuration.
              </p>
            </div>
            <% if form.object.errors[:webhook_url].any? %>
              <p class="mt-2 text-sm text-red-600 flex items-center">
                <svg class="w-4 h-4 mr-1 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                <%= form.object.errors[:webhook_url].first %>
              </p>
            <% end %>
          </div>
        </div>

        <!-- Enhanced Platform Settings -->
        <div class="border-b border-gray-200 pb-8">
          <div class="flex items-center space-x-3 mb-6">
            <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
              </svg>
            </div>
            <div>
              <h2 class="text-xl font-bold text-gray-900">Platform Settings</h2>
              <p class="text-gray-600">Configure how this platform integration will behave.</p>
            </div>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="bg-gradient-to-br from-blue-100 to-indigo-100 rounded-lg p-6">
              <div class="flex items-start space-x-3">
                <div class="mt-1">
                  <%= form.check_box :auto_sync_enabled, 
                      class: "h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-colors",
                      checked: true %>
                </div>
                <div class="flex-1">
                  <%= form.label :auto_sync_enabled, class: "flex items-center text-sm font-semibold text-blue-900 cursor-pointer" do %>
                    <svg class="w-4 h-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                    </svg>
                    Enable automatic synchronization
                  <% end %>
                  <p class="text-sm text-blue-700 mt-2">
                    Automatically sync content and analytics data every hour.
                  </p>
                </div>
              </div>
            </div>
            
            <div class="bg-gradient-to-br from-emerald-100 to-green-100 rounded-lg p-6">
              <div class="flex items-start space-x-3">
                <div class="mt-1">
                  <%= form.check_box :posting_enabled, 
                      class: "h-5 w-5 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded transition-colors",
                      checked: true %>
                </div>
                <div class="flex-1">
                  <%= form.label :posting_enabled, class: "flex items-center text-sm font-semibold text-emerald-900 cursor-pointer" do %>
                    <svg class="w-4 h-4 mr-2 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"/>
                    </svg>
                    Enable content publishing
                  <% end %>
                  <p class="text-sm text-emerald-700 mt-2">
                    Allow AI Marketing Hub to publish content to this platform.
                  </p>
                </div>
              </div>
            </div>
            
            <div class="bg-gradient-to-br from-purple-100 to-indigo-100 rounded-lg p-6 md:col-span-2">
              <div class="flex items-start space-x-3">
                <div class="mt-1">
                  <%= form.check_box :analytics_enabled, 
                      class: "h-5 w-5 text-purple-600 focus:ring-purple-500 border-gray-300 rounded transition-colors",
                      checked: true %>
                </div>
                <div class="flex-1">
                  <%= form.label :analytics_enabled, class: "flex items-center text-sm font-semibold text-purple-900 cursor-pointer" do %>
                    <svg class="w-4 h-4 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                    </svg>
                    Enable analytics collection
                  <% end %>
                  <p class="text-sm text-purple-700 mt-2">
                    Collect performance metrics and engagement data for insights and reporting.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
              </p>
            </div>
            <div class="flex items-center">
              <%= form.check_box :analytics_enabled, 
                  class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded",
                  checked: true %>
              <%= form.label :analytics_enabled, class: "ml-2 block text-sm text-gray-900 flex items-center" do %>
                <svg class="w-4 h-4 mr-1 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2a4 4 0 014-4h2"/></svg>
                Enable analytics collection
              <% end %>
              <p class="ml-6 text-sm text-gray-500">
                Collect performance metrics and engagement data.
              </p>
            </div>
          </div>
        </div>

        <!-- Enhanced OAuth Scopes Information -->
        <div class="bg-gradient-to-br from-emerald-100 via-green-100 to-blue-100 rounded-xl p-8 shadow-sm">
          <div class="flex items-center space-x-4 mb-8">
            <div class="w-12 h-12 bg-gradient-to-br from-emerald-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <div>
              <h3 class="text-xl font-bold text-gray-900 flex items-center">
                Requested Permissions
                <svg class="w-5 h-5 ml-2 text-emerald-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                </svg>
              </h3>
              <p class="text-gray-600 flex items-center mt-1">
                <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                When you connect your account, we'll request these permissions
              </p>
            </div>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <% @oauth_settings[:scopes].each do |scope| %>
              <div class="group bg-white rounded-xl p-4 shadow-sm hover:shadow-md transition-all duration-200 hover:-translate-y-1">
                <div class="flex items-center space-x-3">
                  <div class="w-8 h-8 bg-gradient-to-br from-emerald-400 to-green-500 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                    </svg>
                  </div>
                  <div class="flex-1">
                    <span class="text-sm font-semibold text-gray-900 block leading-tight">
                      <%= scope.humanize %>
                    </span>
                    <div class="w-full bg-emerald-100 rounded-full h-1 mt-2">
                      <div class="bg-gradient-to-r from-emerald-500 to-green-500 h-1 rounded-full w-full"></div>
                    </div>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
          
          <div class="mt-6 flex items-center justify-center">
            <div class="flex items-center space-x-2 text-sm text-gray-600 bg-white rounded-lg px-4 py-2 shadow-sm">
              <svg class="w-4 h-4 text-emerald-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
              </svg>
              <span class="font-medium">Secure OAuth 2.0 authentication</span>
            </div>
          </div>
        </div>

        <!-- Enhanced Form Actions -->
        <div class="flex flex-col sm:flex-row items-center justify-between gap-4 pt-8 border-t border-gray-200">
          <div class="flex items-center space-x-2 text-sm text-gray-500">
            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
            </svg>
            <span>Your credentials are encrypted and stored securely</span>
          </div>
          
          <div class="flex items-center space-x-4">
            <%= link_to platform_configurations_path, 
                class: "inline-flex items-center px-6 py-3 text-sm font-semibold text-gray-700 bg-white hover:bg-gray-50 rounded-xl shadow-sm border border-gray-300 transition-all duration-200 transform hover:scale-105 hover:shadow-md" do %>
              <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
              </svg>
              Cancel
            <% end %>
            
            <%= form.submit "Connect to #{@platform_name.titleize}", 
                class: "inline-flex items-center px-8 py-3 text-base font-bold text-white bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 hover:from-blue-700 hover:via-purple-700 hover:to-indigo-700 rounded-xl shadow-lg hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 transform hover:scale-105" do %>
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
              </svg>
              Connect Platform
            <% end %>
          </div>
        </div>
        </div>
      <% end %>
    </div>
  </div>
</div>
