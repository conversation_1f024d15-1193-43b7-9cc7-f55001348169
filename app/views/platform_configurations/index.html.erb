<%# Platform Configurations Index - Social Media OAuth Management %>
<div class="bg-gray-50 min-h-screen">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    
    <!-- Page Header -->
    <div class="bg-white rounded-xl shadow-sm p-8 mb-8 border-l-4 border-blue-500">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-6">
          <div class="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl shadow-lg">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
            </svg>
          </div>
          <div>
            <div class="flex items-center space-x-2 mb-1">
              <h1 class="text-3xl font-bold text-gray-900">Social Platform Connections</h1>
              <svg class="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <p class="text-gray-600 flex items-center">
              <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
              </svg>
              Connect and manage your social media accounts for seamless content publishing
            </p>
          </div>
        </div>
        <div class="flex items-center space-x-3">
          <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-emerald-100 text-emerald-800 border border-emerald-200">
            <svg class="w-5 h-5 mr-2 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <%= @connected_platforms.count %> Connected
          </span>
          <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-slate-100 text-slate-700 border border-slate-200">
            <svg class="w-5 h-5 mr-2 text-slate-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <%= @available_platforms.count - @connected_platforms.count %> Available
          </span>
        </div>
      </div>
    </div>

    <!-- Platform Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <% @available_platforms.each do |platform| %>
        <% is_connected = @connected_platforms.include?(platform) %>
        <% config = @platform_configurations.find { |pc| pc.platform_name == platform } %>
        
        <div class="bg-white rounded-xl shadow-sm overflow-hidden hover:shadow-lg hover:scale-[1.02] transition-all duration-300 border border-gray-100">
          <!-- Platform Header -->
          <div class="p-6 border-b border-gray-100 bg-gradient-to-r from-gray-50 to-white">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-4">
                <div class="flex-shrink-0 relative">
                  <%= render "shared/platform_icons/#{platform}", css_class: "w-12 h-12" %>
                  <% if is_connected %>
                    <div class="absolute -top-1 -right-1 w-4 h-4 bg-emerald-500 rounded-full border-2 border-white flex items-center justify-center">
                      <svg class="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 8 8">
                        <path d="M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z"/>
                      </svg>
                    </div>
                  <% end %>
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
                    </svg>
                    <%= platform.titleize %>
                  </h3>
                  <p class="text-sm text-gray-600 flex items-center mt-1">
                    <% if is_connected %>
                      <svg class="w-4 h-4 inline text-emerald-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                      </svg>
                      <span class="font-medium text-emerald-700">Connected as <%= config&.connected_account_name || 'Unknown' %></span>
                    <% else %>
                      <svg class="w-4 h-4 inline text-slate-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                      </svg>
                      <span class="text-slate-600">Ready to connect</span>
                    <% end %>
                  </p>
                </div>
              </div>
              <!-- Enhanced Status Badge -->
              <% if is_connected %>
                <div class="flex flex-col items-end space-y-2">
                  <span class="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-semibold bg-emerald-100 text-emerald-800 border border-emerald-200">
                    <svg class="w-3 h-3 mr-1.5 text-emerald-600" fill="currentColor" viewBox="0 0 8 8">
                      <path d="M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z"/>
                    </svg>
                    Active
                  </span>
                  <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                    <%= config.last_sync_at ? time_ago_in_words(config.last_sync_at) + ' ago' : 'Never synced' %>
                  </span>
                </div>
              <% else %>
                <span class="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-semibold bg-slate-100 text-slate-700 border border-slate-200">
                  <svg class="w-3 h-3 mr-1.5 text-slate-500" fill="currentColor" viewBox="0 0 8 8">
                    <circle cx="4" cy="4" r="3"/>
                  </svg>
                  Available
                </span>
              <% end %>
            </div>
          </div>

          <!-- Platform Details -->
          <div class="p-6">
            <% if is_connected %>
              <!-- Connected Platform Info -->
              <div class="space-y-4 mb-6">
                <div class="grid grid-cols-2 gap-4">
                  <div class="bg-gray-50 rounded-lg p-3">
                    <div class="flex items-center justify-between">
                      <div class="flex items-center space-x-2">
                        <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        <span class="text-sm font-medium text-gray-600">Last Sync</span>
                      </div>
                      <span class="text-sm font-semibold text-gray-900">
                        <%= config.last_sync_at ? time_ago_in_words(config.last_sync_at) + ' ago' : 'Never' %>
                      </span>
                    </div>
                  </div>
                  <div class="bg-gray-50 rounded-lg p-3">
                    <div class="flex items-center justify-between">
                      <div class="flex items-center space-x-2">
                        <svg class="w-4 h-4 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                        </svg>
                        <span class="text-sm font-medium text-gray-600">Auto Sync</span>
                      </div>
                      <span class="text-sm font-semibold <%= config.auto_sync_enabled? ? 'text-emerald-700' : 'text-slate-600' %>">
                        <%= config.auto_sync_enabled? ? 'Enabled' : 'Disabled' %>
                      </span>
                    </div>
                  </div>
                </div>
                <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-3 border border-blue-100">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                      <svg class="w-4 h-4 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"/>
                      </svg>
                      <span class="text-sm font-medium text-indigo-700">Publishing Status</span>
                    </div>
                    <span class="text-sm font-semibold <%= config.posting_enabled? ? 'text-emerald-700' : 'text-slate-600' %>">
                      <%= config.posting_enabled? ? 'Active' : 'Paused' %>
                    </span>
                  </div>
                </div>
              </div>

              <!-- Enhanced Action Buttons -->
              <div class="flex space-x-3">
                <%= link_to platform_configuration_path(platform), 
                    class: "flex-1 inline-flex items-center justify-center px-4 py-3 shadow-sm text-sm font-semibold rounded-lg text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 transition-all duration-200 transform hover:scale-105" do %>
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                  </svg>
                  Manage
                <% end %>
                <button type="button" 
                        class="px-4 py-3 shadow-sm text-sm font-semibold rounded-lg text-emerald-700 bg-emerald-100 hover:bg-emerald-200 border border-emerald-200 transition-all duration-200 transform hover:scale-105"
                        onclick="testConnection('<%= platform %>')"
                        title="Test platform connection">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                </button>
              </div>
            <% else %>
              <!-- Not Connected -->
              <div class="text-center py-8">
                <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center">
                  <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
                  </svg>
                </div>
                <h4 class="text-lg font-semibold text-gray-900 mb-2">Ready to Connect</h4>
                <p class="text-gray-600 text-sm mb-6 max-w-xs mx-auto">
                  Connect your <%= platform.titleize %> account to start publishing content automatically and track performance metrics.
                </p>
                
                <%= link_to new_platform_configuration_path(platform: platform), 
                    class: "inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl" do %>
                  <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                  </svg>
                  Connect <%= platform.titleize %>
                <% end %>
              </div>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>

    <!-- Enhanced Help Section -->
    <div class="bg-white rounded-xl shadow-sm p-8 mt-8 border-l-4 border-indigo-500">
      <div class="flex items-start space-x-6">
        <div class="flex-shrink-0">
          <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
        </div>
        <div class="flex-1">
          <div class="flex items-center space-x-2 mb-3">
            <h3 class="text-xl font-bold text-gray-900">Need Help Connecting Platforms?</h3>
            <svg class="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
          <p class="text-gray-600 mb-6 text-lg">
            Each platform requires you to create a developer application to obtain API credentials. 
            We'll guide you through the process for each platform with step-by-step instructions.
          </p>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-100">
              <div class="flex items-center space-x-3 mb-4">
                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                </svg>
                <h4 class="text-lg font-semibold text-blue-900">What you'll need:</h4>
              </div>
              <ul class="space-y-3 text-sm text-blue-800">
                <li class="flex items-center space-x-2">
                  <svg class="w-4 h-4 text-blue-600 flex-shrink-0" fill="currentColor" viewBox="0 0 8 8">
                    <path d="M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z"/>
                  </svg>
                  <span>Developer account on each platform</span>
                </li>
                <li class="flex items-center space-x-2">
                  <svg class="w-4 h-4 text-blue-600 flex-shrink-0" fill="currentColor" viewBox="0 0 8 8">
                    <path d="M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z"/>
                  </svg>
                  <span>API credentials (Client ID & Secret)</span>
                </li>
                <li class="flex items-center space-x-2">
                  <svg class="w-4 h-4 text-blue-600 flex-shrink-0" fill="currentColor" viewBox="0 0 8 8">
                    <path d="M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z"/>
                  </svg>
                  <span>Webhook URL configuration</span>
                </li>
              </ul>
            </div>
            <div class="bg-gradient-to-br from-emerald-50 to-green-50 rounded-lg p-6 border border-emerald-100">
              <div class="flex items-center space-x-3 mb-4">
                <svg class="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                </svg>
                <h4 class="text-lg font-semibold text-emerald-900">What you'll get:</h4>
              </div>
              <ul class="space-y-3 text-sm text-emerald-800">
                <li class="flex items-center space-x-2">
                  <svg class="w-4 h-4 text-emerald-600 flex-shrink-0" fill="currentColor" viewBox="0 0 8 8">
                    <path d="M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z"/>
                  </svg>
                  <span>Automated content publishing</span>
                </li>
                <li class="flex items-center space-x-2">
                  <svg class="w-4 h-4 text-emerald-600 flex-shrink-0" fill="currentColor" viewBox="0 0 8 8">
                    <path d="M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z"/>
                  </svg>
                  <span>Real-time analytics sync</span>
                </li>
                <li class="flex items-center space-x-2">
                  <svg class="w-4 h-4 text-emerald-600 flex-shrink-0" fill="currentColor" viewBox="0 0 8 8">
                    <path d="M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z"/>
                  </svg>
                  <span>Centralized social management</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- JavaScript for Connection Testing -->
<script>
function testConnection(platform) {
  const button = event.target;
  const originalContent = button.innerHTML;
  
  // Show loading state
  button.innerHTML = '<svg class="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>';
  button.disabled = true;
  
  fetch(`/platform_configurations/${platform}/test_connection`, {
    method: 'GET',
    headers: {
      'Accept': 'application/json',
      'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      // Show success briefly
      button.innerHTML = '<svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>';
      button.classList.add('text-green-600');
      
      setTimeout(() => {
        button.innerHTML = originalContent;
        button.classList.remove('text-green-600');
        button.disabled = false;
      }, 2000);
    } else {
      // Show error briefly
      button.innerHTML = '<svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>';
      button.classList.add('text-red-600');
      
      setTimeout(() => {
        button.innerHTML = originalContent;
        button.classList.remove('text-red-600');
        button.disabled = false;
      }, 2000);
      
      alert(data.message || 'Connection test failed');
    }
  })
  .catch(error => {
    console.error('Connection test error:', error);
    button.innerHTML = originalContent;
    button.disabled = false;
    alert('Failed to test connection. Please try again.');
  });
}
</script>
