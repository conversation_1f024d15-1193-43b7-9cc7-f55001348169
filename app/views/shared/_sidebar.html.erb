<!-- Clean Modern Sidebar -->
<nav id="sidebar" class="fixed lg:static inset-y-0 left-0 z-50 w-64 lg:w-16 xl:w-64 bg-white border-r border-gray-200 transform -translate-x-full lg:translate-x-0 transition-transform duration-300 ease-in-out">
  <div class="flex flex-col h-full pt-16 lg:pt-0">
    
    <!-- Sidebar Header (Mobile Only) -->
    <div class="flex items-center justify-between p-4 border-b border-gray-200 lg:hidden">
      <h2 class="text-lg font-semibold text-gray-900">AI Marketing Hub</h2>
      <button id="sidebar-close" class="p-2 text-gray-400 hover:text-gray-600 transition-colors">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <!-- Brand Logo/Title (Compact Mode) -->
    <div class="p-4 flex items-center justify-center border-b border-gray-200 lg:block xl:hidden">
      <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
        </svg>
      </div>
    </div>
    
    <!-- Brand Logo/Title (Full Mode) -->
    <div class="hidden xl:flex items-center p-4 border-b border-gray-200">
      <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
        </svg>
      </div>
      <div class="ml-3">
        <h1 class="text-lg font-bold text-gray-900">AI Marketing Hub</h1>
        <p class="text-xs text-gray-500">Marketing Platform</p>
      </div>
    </div>

    <!-- Navigation Links -->
    <div class="flex-1 px-3 py-4 space-y-1 overflow-y-auto">
      
      <!-- Dashboard -->
      <%= link_to dashboard_path, class: "group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 #{ current_page?(dashboard_path) ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700' : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900' }", data: { nav: "dashboard" } do %>
        <svg class="w-5 h-5 mr-3 lg:mr-0 xl:mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
          <path stroke-linecap="round" stroke-linejoin="round" d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
        </svg>
        <span class="lg:hidden xl:block">Dashboard</span>
        <% if current_page?(dashboard_path) %>
          <span class="ml-auto w-2 h-2 bg-blue-600 rounded-full lg:hidden xl:block"></span>
        <% end %>
      <% end %>

      <!-- Campaigns -->
      <%= link_to campaigns_path, class: "group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 #{ current_page?(campaigns_path) || controller_name == 'campaigns' ? 'bg-purple-50 text-purple-700 border-r-2 border-purple-700' : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900' }", data: { nav: "campaigns" } do %>
        <svg class="w-5 h-5 mr-3 lg:mr-0 xl:mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
          <path stroke-linecap="round" stroke-linejoin="round" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
        </svg>
        <span class="lg:hidden xl:block">Campaigns</span>
        <% if current_page?(campaigns_path) || controller_name == 'campaigns' %>
          <span class="ml-auto w-2 h-2 bg-purple-600 rounded-full lg:hidden xl:block"></span>
        <% end %>
      <% end %>

      <!-- Vibe Analytics -->
      <%= link_to vibe_analytics_path, class: "group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 #{ current_page?(vibe_analytics_path) || controller_name == 'vibe_analytics' ? 'bg-pink-50 text-pink-700 border-r-2 border-pink-700' : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900' }", data: { nav: "vibe" } do %>
        <svg class="w-5 h-5 mr-3 lg:mr-0 xl:mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
          <path stroke-linecap="round" stroke-linejoin="round" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
        </svg>
        <span class="lg:hidden xl:block">Vibe Analytics</span>
        <span class="ml-auto text-xs bg-pink-100 text-pink-700 px-2 py-0.5 rounded-full lg:hidden xl:block">New</span>
      <% end %>

      <!-- Audiences -->
      <%= link_to audiences_path, class: "group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 #{ current_page?(audiences_path) || controller_name == 'audiences' ? 'bg-emerald-50 text-emerald-700 border-r-2 border-emerald-700' : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900' }", data: { nav: "audiences" } do %>
        <svg class="w-5 h-5 mr-3 lg:mr-0 xl:mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
          <path stroke-linecap="round" stroke-linejoin="round" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
        </svg>
        <span class="lg:hidden xl:block">Audiences</span>
        <span class="ml-auto text-xs bg-emerald-100 text-emerald-700 px-2 py-0.5 rounded-full lg:hidden xl:block">Live</span>
      <% end %>

      <!-- Platform Configurations -->
      <%= link_to platform_configurations_path, class: "group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 #{ current_page?(platform_configurations_path) || controller_name == 'platform_configurations' ? 'bg-orange-50 text-orange-700 border-r-2 border-orange-700' : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900' }", data: { nav: "platforms" } do %>
        <svg class="w-5 h-5 mr-3 lg:mr-0 xl:mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
          <path stroke-linecap="round" stroke-linejoin="round" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"/>
        </svg>
        <span class="lg:hidden xl:block">Platforms</span>
      <% end %>

      <!-- AI Agents -->
      <%= link_to ai_agents_path, class: "group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 #{ current_page?(ai_agents_path) || controller_name == 'ai_agents' ? 'bg-indigo-50 text-indigo-700 border-r-2 border-indigo-700' : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900' }", data: { nav: "ai_agents" } do %>
        <svg class="w-5 h-5 mr-3 lg:mr-0 xl:mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
          <path stroke-linecap="round" stroke-linejoin="round" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
        </svg>
        <span class="lg:hidden xl:block">AI Agents</span>
        <span class="ml-auto text-xs bg-indigo-100 text-indigo-700 px-2 py-0.5 rounded-full lg:hidden xl:block">Live</span>
        <% if current_page?(ai_agents_path) || controller_name == 'ai_agents' %>
          <span class="ml-auto w-2 h-2 bg-indigo-600 rounded-full lg:hidden xl:block"></span>
        <% end %>
      <% end %>

      <!-- Section Divider -->
      <div class="py-3">
        <div class="border-t border-gray-200"></div>
      </div>

      <!-- Admin Section (only for admin users) -->
      <% if current_user.admin_or_owner? %>
        <div class="pb-2">
          <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider lg:hidden xl:block">
            Administration
          </h3>
        </div>

        <!-- AI Provider Configurations -->
        <%= link_to admin_ai_provider_configurations_path, class: "group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 #{ controller_name == 'ai_provider_configurations' && params[:controller].include?('admin') ? 'bg-red-50 text-red-700 border-r-2 border-red-700' : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900' }", data: { nav: "admin_ai_providers" } do %>
          <svg class="w-5 h-5 mr-3 lg:mr-0 xl:mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"/>
          </svg>
          <span class="lg:hidden xl:block">AI Providers</span>
          <% if controller_name == 'ai_provider_configurations' && params[:controller].include?('admin') %>
            <span class="ml-auto w-2 h-2 bg-red-600 rounded-full lg:hidden xl:block"></span>
          <% end %>
        <% end %>

        <!-- Section Divider -->
        <div class="py-3">
          <div class="border-t border-gray-200"></div>
        </div>
      <% end %>

      <!-- Settings -->
      <div class="group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 text-gray-700 hover:bg-gray-50 hover:text-gray-900 cursor-pointer">
        <svg class="w-5 h-5 mr-3 lg:mr-0 xl:mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
          <path stroke-linecap="round" stroke-linejoin="round" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
          <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
        </svg>
        <span class="lg:hidden xl:block">Settings</span>
      </div>
    </div>

    <!-- Sidebar Footer -->
    <div class="p-4 border-t border-gray-200">
      <div class="flex items-center">
        <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
          <span class="text-white text-sm font-semibold">
            <%= current_user.email.first.upcase %>
          </span>
        </div>
        <div class="ml-3 lg:hidden xl:block">
          <p class="text-sm font-medium text-gray-900 truncate">
            <%= current_user.email.split('@').first.titleize %>
          </p>
          <p class="text-xs text-gray-500">
            <%= current_user.tenant.name if current_user.tenant %>
          </p>
        </div>
        <!-- Logout Button -->
        <div class="ml-auto lg:hidden xl:block">
          <%= link_to destroy_user_session_path, method: :delete, 
              class: "p-2 text-gray-400 hover:text-gray-600 rounded-lg transition-colors",
              title: "Sign Out" do %>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
              <path stroke-linecap="round" stroke-linejoin="round" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
            </svg>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</nav>