# frozen_string_literal: true

# Create seed data for AI Marketing Hub demo

puts "🌱 Seeding AI Marketing Hub..."

# Create demo tenant
demo_tenant = Tenant.find_or_create_by!(
  name: "Demo Corp",
  subdomain: "demo-corp"
) do |tenant|
  tenant.settings = {
    branding: {
      primary_color: "#3B82F6",
      logo_url: "https://via.placeholder.com/150x50/3B82F6/white?text=Demo+Corp"
    },
    features: {
      ai_enabled: true,
      email_campaigns: true,
      social_media: true,
      seo_tools: true
    }
  }
end

puts "✅ Created demo tenant: #{demo_tenant.name}"

# Create demo users
ActsAsTenant.with_tenant(demo_tenant) do
  # Create owner user
  owner_user = User.find_or_create_by!(email: "<EMAIL>") do |user|
    user.password = "password123"
    user.password_confirmation = "password123"
    user.first_name = "Sarah"
    user.last_name = "Johnson"
    user.role = "owner"
    user.confirmed_at = Time.current
    user.tenant = demo_tenant
  end

  # Create admin user
  admin_user = User.find_or_create_by!(email: "<EMAIL>") do |user|
    user.password = "password123"
    user.password_confirmation = "password123"
    user.first_name = "Mike"
    user.last_name = "<PERSON>"
    user.role = "admin"
    user.confirmed_at = Time.current
    user.tenant = demo_tenant
  end

  puts "✅ Created demo users: #{owner_user.full_name} (Owner), #{admin_user.full_name} (Admin)"

  # Create sample campaigns

  # 1. Active Email Campaign
  email_campaign = Campaign.find_or_create_by!(
    name: "Holiday Sale 2024",
    tenant: demo_tenant
  ) do |campaign|
    campaign.assign_attributes(
      description: "Black Friday and Cyber Monday promotional campaign targeting existing customers",
      campaign_type: "email",
      status: "active",
      target_audience: "Existing customers who purchased in last 12 months",
      start_date: 1.week.ago,
      end_date: 1.week.from_now,
      budget_cents: 50000, # $500
      created_by: owner_user,
      settings: {
        goals: {
          primary: "drive_sales",
          secondary: "increase_engagement",
          target_revenue: 25000
        }
      }
    )
  end

  EmailCampaign.find_or_create_by(campaign: email_campaign) do |ec|
    ec.subject_line = "🎉 Exclusive Holiday Sale - Up to 50% Off Everything!"
    ec.preview_text = "Don't miss our biggest sale of the year. Limited time only!"
    ec.content = "Hi {{first_name}},\n\nOur biggest sale of the year is here! Get up to 50% off everything in our store.\n\nUse code HOLIDAY50 at checkout.\n\nSale ends December 2nd - don't wait!\n\nShop now: [Shop Button]"
    ec.from_name = "Demo Corp Team"
    ec.from_email = "<EMAIL>"
    ec.settings = {
      delivery_options: {
        send_immediately: false,
        scheduled_at: 2.hours.from_now.iso8601
      },
      tracking: {
        opens: true,
        clicks: true,
        unsubscribes: true
      },
      recipient_count: 12500
    }
  end

  # 2. Active Social Campaign
  social_campaign = Campaign.find_or_create_by!(
    name: "Brand Awareness Q4 2025",
    tenant: demo_tenant
  ) do |campaign|
    campaign.assign_attributes(
      description: "Multi-platform social media campaign to increase brand awareness and engagement",
      campaign_type: "social",
      status: "active",
      target_audience: "Tech-savvy professionals aged 25-45",
      start_date: 2.weeks.ago,
      end_date: 2.weeks.from_now,
      budget_cents: 30000, # $300
      created_by: admin_user,
      settings: {
        goals: {
          primary: "increase_brand_awareness",
          target_impressions: 100000,
          target_engagement_rate: 0.05
        }
      }
    )
  end

  SocialCampaign.find_or_create_by(campaign: social_campaign) do |sc|
    sc.platforms = [ "twitter", "linkedin", "facebook" ]
    sc.content_variants = {
      "twitter" => "🚀 Exciting news! Our AI marketing platform is helping SMBs compete with enterprise tools. Join the revolution! #MarketingAI #SMB",
      "linkedin" => "We're proud to announce that our AI marketing automation platform is now helping small and medium businesses access enterprise-level marketing tools. See how we're democratizing advanced marketing technology.",
      "facebook" => "Great news for small business owners! 🎉 Our new AI marketing platform makes professional campaign management accessible and affordable. Check out how we're changing the game!"
    }
    sc.hashtags = "#MarketingAI #SMB #Automation #BusinessGrowth"
    sc.target_demographics = {
      age_range: "25-45",
      interests: [ "business", "technology", "marketing", "entrepreneurship" ],
      location: [ "United States", "Canada", "United Kingdom" ]
    }
    sc.social_settings = {
      follower_counts: {
        twitter: 8500,
        linkedin: 5200,
        facebook: 12000
      },
      engagement_rate: 0.08
    }
  end

  # 3. Draft Multi-channel Campaign
  Campaign.find_or_create_by!(
    name: "New Product Launch 2025",
    tenant: demo_tenant
  ) do |campaign|
    campaign.assign_attributes(
      description: "Comprehensive launch campaign for our new AI analytics feature",
      campaign_type: "multi_channel",
      status: "draft",
      target_audience: "Current users and prospects interested in analytics",
      start_date: 1.month.from_now,
      end_date: 2.months.from_now,
      budget_cents: 100000, # $1000
      created_by: owner_user,
      settings: {
        goals: {
          primary: "product_launch",
          target_signups: 500,
          target_revenue: 50000
        }
      }
    )
  end

  # 4. Completed Campaign
  Campaign.find_or_create_by!(
    name: "Customer Onboarding Series 2024",
    tenant: demo_tenant
  ) do |campaign|
    campaign.assign_attributes(
      description: "Welcome email series for new customers",
      campaign_type: "email",
      status: "completed",
      target_audience: "New customers in first 30 days",
      start_date: 2.months.ago,
      end_date: 1.month.ago,
      budget_cents: 15000, # $150
      created_by: admin_user,
      settings: {
        goals: {
          primary: "improve_onboarding",
          target_completion_rate: 0.75
        }
      }
    )
  end

  # 5. Paused Campaign
  Campaign.find_or_create_by!(
    name: "Summer Promotion 2024",
    tenant: demo_tenant
  ) do |campaign|
    campaign.assign_attributes(
      description: "Summer sale campaign - paused due to inventory issues",
      campaign_type: "social",
      status: "paused",
      target_audience: "All customers and prospects",
      start_date: 1.month.ago,
      end_date: 2.weeks.from_now,
      budget_cents: 40000, # $400
      created_by: owner_user,        settings: {
          goals: {
            primary: "drive_summer_sales",
            target_revenue: 30000
          }
        }
      )
    end

  puts "✅ Created 5 sample campaigns with various statuses"

  # Add Vibe Marketing data to existing campaigns
  puts "🎨 Adding Vibe Marketing data to campaigns..."

  Campaign.find_each do |campaign|
    # Add vibe status based on campaign type and status
    vibe_status = case campaign.status
    when 'active' then [ 'vibe_approved', 'analyzing' ].sample
    when 'completed' then 'vibe_approved'
    when 'paused' then [ 'vibe_flagged', 'analyzing' ].sample
    else 'pending'
    end

    # Add emotional tone based on campaign name/type
    emotional_tone = case campaign.name.downcase
    when /holiday|sale/ then 'excited'
    when /brand|awareness/ then 'confident'
    when /launch/ then 'anticipatory'
    when /onboarding/ then 'welcoming'
    when /summer|promotion/ then 'energetic'
    else 'positive'
    end

    # Add cultural relevance and authenticity scores
    cultural_score = rand(75.0..95.0).round(1)
    authenticity_score = rand(80.0..95.0).round(1)

    campaign.update!(
      vibe_status: vibe_status,
      emotional_tone: emotional_tone,
      cultural_relevance_score: cultural_score,
      authenticity_score: authenticity_score
    )
  end

  # Create sample audiences for Vibe Analytics
  puts "👥 Creating sample audiences..."

  audiences_data = [
    {
      name: "Tech Enthusiasts",
      description: "Early adopters and technology enthusiasts who love innovation",
      cultural_context: "western",
      primary_language: "en",
      age_range_min: 25,
      age_range_max: 45,
      interests: [ "technology", "gadgets", "innovation" ],
      cultural_alignment_score: 92.5
    },
    {
      name: "Budget-Conscious Shoppers",
      description: "Price-sensitive consumers who research before buying",
      cultural_context: "western",
      primary_language: "en",
      age_range_min: 30,
      age_range_max: 55,
      interests: [ "savings", "deals", "value" ],
      cultural_alignment_score: 78.0
    },
    {
      name: "Young Professionals",
      description: "Career-focused individuals in urban areas",
      cultural_context: "western",
      primary_language: "en",
      age_range_min: 22,
      age_range_max: 35,
      interests: [ "career", "networking", "productivity" ],
      cultural_alignment_score: 85.5
    }
  ]

  audiences = audiences_data.map do |audience_data|
    audience = Audience.find_or_create_by!(
      name: audience_data[:name],
      tenant: demo_tenant
    ) do |a|
      audience_data.each { |key, value| a.send("#{key}=", value) }
      a.created_by = owner_user
      a.target_demographics = "#{audience_data[:age_range_min]}-#{audience_data[:age_range_max]} year olds"
      a.geographic_regions = [ "North America", "Europe" ]
      a.behavioral_traits = [ "engaged", "research_oriented" ]
      a.communication_preferences = "Direct, value-focused communication"
      a.cultural_values = "Efficiency, value, innovation"
    end

    puts "  ✅ Created audience: #{audience.name}"
    audience
  end

  # Create campaign-audience associations
  puts "🔗 Creating campaign-audience associations..."

  Campaign.find_each.with_index do |campaign, index|
    audience = audiences[index % audiences.length]

    CampaignAudience.find_or_create_by!(
      campaign: campaign,
      audience: audience,
      tenant: demo_tenant
    )
  end

  # Create campaign metrics for analytics
  puts "📊 Creating campaign metrics..."

  Campaign.find_each do |campaign|
    CampaignMetric.find_or_create_by(
      campaign: campaign,
      metric_date: campaign.start_date || Date.current
    ) do |metric|
      metric.impressions = rand(5000..50000)
      metric.clicks = (metric.impressions * rand(0.01..0.05)).to_i
      metric.conversions = (metric.clicks * rand(0.02..0.08)).to_i
      metric.revenue_cents = rand(1000..10000)
      metric.cost_cents = rand(500..5000)
      metric.email_opens = rand(100..1000)
      metric.email_clicks = rand(10..100)
      metric.email_bounces = rand(5..50)
      metric.social_engagements = rand(50..500)
      metric.social_shares = rand(10..100)
      metric.social_comments = rand(5..50)
      metric.seo_organic_traffic = rand(100..1000)
    end
  end

  puts "✅ Created Vibe Marketing and Audiences data"

  # Create AI Provider Configurations
  puts "🤖 Creating AI provider configurations..."

  # OpenAI configurations
  AiProviderConfiguration.find_or_create_by!(
    tenant: demo_tenant,
    provider_name: 'openai',
    ai_model_name: 'gpt-4o'
  ) do |config|
    config.cost_per_token = 0.00003
    config.is_active = true
    config.default_for_task_type = 'creative_content'
    config.capabilities = [ 'text', 'vision', 'function_calling' ]
    config.configuration = {
      'max_tokens' => 4096,
      'temperature' => 0.8,
      'timeout_seconds' => 180,
      'retry_attempts' => 3
    }
  end

  AiProviderConfiguration.find_or_create_by!(
    tenant: demo_tenant,
    provider_name: 'openai',
    ai_model_name: 'gpt-4o-mini'
  ) do |config|
    config.cost_per_token = 0.0000015
    config.is_active = true
    config.default_for_task_type = 'general'
    config.capabilities = [ 'text', 'function_calling' ]
    config.configuration = {
      'max_tokens' => 2048,
      'temperature' => 0.7,
      'timeout_seconds' => 180,
      'retry_attempts' => 3
    }
  end

  # Anthropic configurations
  AiProviderConfiguration.find_or_create_by!(
    tenant: demo_tenant,
    provider_name: 'anthropic',
    ai_model_name: 'claude-3.5-sonnet'
  ) do |config|
    config.cost_per_token = 0.000015
    config.is_active = true
    config.default_for_task_type = 'complex_reasoning'
    config.capabilities = [ 'text', 'vision', 'function_calling' ]
    config.configuration = {
      'max_tokens' => 4096,
      'temperature' => 0.7,
      'timeout_seconds' => 180,
      'retry_attempts' => 3
    }
  end

  AiProviderConfiguration.find_or_create_by!(
    tenant: demo_tenant,
    provider_name: 'anthropic',
    ai_model_name: 'claude-3-opus'
  ) do |config|
    config.cost_per_token = 0.000075
    config.is_active = true
    config.default_for_task_type = 'data_analysis'
    config.capabilities = [ 'text', 'vision', 'function_calling' ]
    config.configuration = {
      'max_tokens' => 4096,
      'temperature' => 0.1,
      'timeout_seconds' => 300,
      'retry_attempts' => 5
    }
  end

  # Google Gemini configurations
  AiProviderConfiguration.find_or_create_by!(
    tenant: demo_tenant,
    provider_name: 'gemini',
    ai_model_name: 'gemini-1.5-pro'
  ) do |config|
    config.cost_per_token = 0.0000035
    config.is_active = true
    config.default_for_task_type = 'vision_tasks'
    config.capabilities = [ 'text', 'vision', 'function_calling' ]
    config.configuration = {
      'max_tokens' => 4096,
      'temperature' => 0.7,
      'timeout_seconds' => 180,
      'retry_attempts' => 3
    }
  end

  AiProviderConfiguration.find_or_create_by!(
    tenant: demo_tenant,
    provider_name: 'gemini',
    ai_model_name: 'gemini-1.5-flash'
  ) do |config|
    config.cost_per_token = 0.00000035
    config.is_active = true
    config.default_for_task_type = 'cost_sensitive'
    config.capabilities = [ 'text', 'function_calling' ]
    config.configuration = {
      'max_tokens' => 2048,
      'temperature' => 0.7,
      'timeout_seconds' => 120,
      'retry_attempts' => 3
    }
  end

  # DeepSeek configuration
  AiProviderConfiguration.find_or_create_by!(
    tenant: demo_tenant,
    provider_name: 'deepseek',
    ai_model_name: 'deepseek-chat'
  ) do |config|
    config.cost_per_token = 0.00000014
    config.is_active = true
    config.default_for_task_type = 'real_time_chat'
    config.capabilities = [ 'text', 'function_calling' ]
    config.configuration = {
      'max_tokens' => 1024,
      'temperature' => 0.7,
      'timeout_seconds' => 60,
      'retry_attempts' => 2
    }
  end

  puts "✅ Created AI provider configurations"

  # Create AI Agents
  puts "🤖 Creating AI agents..."

  AiAgent.find_or_create_by!(
    tenant: demo_tenant,
    agent_type: 'marketing_manager'
  ) do |agent|
    agent.name = 'Marketing Manager'
    agent.description = 'Orchestrates multi-channel campaigns and strategic planning'
    agent.status = 'active'
    agent.configuration = {
      'color' => 'blue',
      'specialty' => 'Campaign Strategy',
      'max_concurrent_tasks' => 10,
      'retry_attempts' => 3,
      'timeout_seconds' => 300
    }
    agent.metrics = {
      'tasks_completed' => 156,
      'success_rate' => 94.2,
      'total_requests' => 167,
      'failed_requests' => 11,
      'average_response_time' => 2.3,
      'total_cost' => 45.67,
      'last_active' => 2.minutes.ago.iso8601,
      'created_at' => Time.current.iso8601
    }
  end

  AiAgent.find_or_create_by!(
    tenant: demo_tenant,
    agent_type: 'email_specialist'
  ) do |agent|
    agent.name = 'Email Specialist'
    agent.description = 'Creates engaging email content and optimizes deliverability'
    agent.status = 'active'
    agent.configuration = {
      'color' => 'green',
      'specialty' => 'Email Marketing',
      'max_concurrent_tasks' => 8,
      'retry_attempts' => 3,
      'timeout_seconds' => 240
    }
    agent.metrics = {
      'tasks_completed' => 89,
      'success_rate' => 91.8,
      'total_requests' => 97,
      'failed_requests' => 8,
      'average_response_time' => 1.8,
      'total_cost' => 23.45,
      'last_active' => 5.minutes.ago.iso8601,
      'created_at' => Time.current.iso8601
    }
  end

  AiAgent.find_or_create_by!(
    tenant: demo_tenant,
    agent_type: 'social_media_agent'
  ) do |agent|
    agent.name = 'Social Media Agent'
    agent.description = 'Manages social content creation and engagement tracking'
    agent.status = 'active'
    agent.configuration = {
      'color' => 'purple',
      'specialty' => 'Social Media',
      'max_concurrent_tasks' => 12,
      'retry_attempts' => 2,
      'timeout_seconds' => 180
    }
    agent.metrics = {
      'tasks_completed' => 234,
      'success_rate' => 88.7,
      'total_requests' => 264,
      'failed_requests' => 30,
      'average_response_time' => 1.4,
      'total_cost' => 67.89,
      'last_active' => 1.minute.ago.iso8601,
      'created_at' => Time.current.iso8601
    }
  end

  AiAgent.find_or_create_by!(
    tenant: demo_tenant,
    agent_type: 'seo_specialist'
  ) do |agent|
    agent.name = 'SEO Specialist'
    agent.description = 'Optimizes content for search engines and keyword performance'
    agent.status = 'active'
    agent.configuration = {
      'color' => 'orange',
      'specialty' => 'SEO & Content',
      'max_concurrent_tasks' => 6,
      'retry_attempts' => 4,
      'timeout_seconds' => 360
    }
    agent.metrics = {
      'tasks_completed' => 67,
      'success_rate' => 92.1,
      'total_requests' => 73,
      'failed_requests' => 6,
      'average_response_time' => 3.2,
      'total_cost' => 18.92,
      'last_active' => 15.minutes.ago.iso8601,
      'created_at' => Time.current.iso8601
    }
  end

  AiAgent.find_or_create_by!(
    tenant: demo_tenant,
    agent_type: 'analytics_agent'
  ) do |agent|
    agent.name = 'Analytics Agent'
    agent.description = 'Provides insights and performance analysis across all channels'
    agent.status = 'active'
    agent.configuration = {
      'color' => 'indigo',
      'specialty' => 'Data Analysis',
      'max_concurrent_tasks' => 5,
      'retry_attempts' => 5,
      'timeout_seconds' => 420
    }
    agent.metrics = {
      'tasks_completed' => 78,
      'success_rate' => 96.5,
      'total_requests' => 81,
      'failed_requests' => 3,
      'average_response_time' => 4.1,
      'total_cost' => 32.14,
      'last_active' => 3.minutes.ago.iso8601,
      'created_at' => Time.current.iso8601
    }
  end

  AiAgent.find_or_create_by!(
    tenant: demo_tenant,
    agent_type: 'customer_experience'
  ) do |agent|
    agent.name = 'Customer Experience Agent'
    agent.description = 'Optimizes customer journey and engagement touchpoints'
    agent.status = 'training'
    agent.configuration = {
      'color' => 'pink',
      'specialty' => 'CX Optimization',
      'max_concurrent_tasks' => 4,
      'retry_attempts' => 3,
      'timeout_seconds' => 300
    }
    agent.metrics = {
      'tasks_completed' => 23,
      'success_rate' => 85.4,
      'total_requests' => 27,
      'failed_requests' => 4,
      'average_response_time' => 2.8,
      'total_cost' => 8.76,
      'last_active' => 30.minutes.ago.iso8601,
      'created_at' => Time.current.iso8601
    }
  end

  puts "✅ Created AI agents"
end

puts "🎉 Seeding completed! Demo data ready with Vibe Analytics."
puts ""
puts "📈 Summary:"
puts "  - #{Campaign.count} campaigns with vibe data"
puts "  - #{Audience.count} audiences created"
puts "  - #{CampaignAudience.count} campaign-audience associations"
puts "  - #{CampaignMetric.count} campaign metrics"
puts ""
puts "Demo login credentials:"
puts "Owner: <EMAIL> / password123"
puts "Admin: <EMAIL> / password123"
puts ""
puts "🌐 Available dashboards:"
puts "  - Main Dashboard: http://localhost:3000/dashboard"
puts "  - Vibe Analytics: http://localhost:3000/vibe_analytics"
puts "  - Audiences: http://localhost:3000/audiences"
puts "  - Campaigns: http://localhost:3000/campaigns"
