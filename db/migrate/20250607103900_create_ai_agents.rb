class CreateAiAgents < ActiveRecord::Migration[8.0]
  def change
    create_table :ai_agents do |t|
      t.references :tenant, null: false, foreign_key: true
      t.string :name, null: false
      t.string :agent_type, null: false
      t.text :description
      t.string :status, null: false, default: 'active'
      t.jsonb :configuration, null: false, default: {}
      t.jsonb :metrics, null: false, default: {}

      t.timestamps
    end

    add_index :ai_agents, :status
    add_index :ai_agents, :agent_type
    add_index :ai_agents, [ :tenant_id, :agent_type ], unique: true
    add_index :ai_agents, :configuration, using: :gin
    add_index :ai_agents, :metrics, using: :gin
  end
end
