class CreateAiProviderConfigurations < ActiveRecord::Migration[8.0]
  def change
    create_table :ai_provider_configurations do |t|
      t.references :tenant, null: false, foreign_key: true
      t.string :provider_name, null: false
      t.string :ai_model_name, null: false
      t.jsonb :configuration, null: false, default: {}
      t.boolean :is_active, null: false, default: true
      t.string :default_for_task_type
      t.decimal :cost_per_token, precision: 10, scale: 8
      t.jsonb :capabilities, null: false, default: []

      t.timestamps
    end

    add_index :ai_provider_configurations, :provider_name
    add_index :ai_provider_configurations, :ai_model_name
    add_index :ai_provider_configurations, :default_for_task_type
    add_index :ai_provider_configurations, :is_active
    add_index :ai_provider_configurations, [ :tenant_id, :provider_name, :ai_model_name ],
              unique: true, name: 'index_ai_provider_configs_on_tenant_provider_model'
    add_index :ai_provider_configurations, :configuration, using: :gin
    add_index :ai_provider_configurations, :capabilities, using: :gin
  end
end
