# frozen_string_literal: true

namespace :campaign_metrics do
  desc "Calculate and update engagement_rate for all existing campaign metrics"
  task update_engagement_rates: :environment do
    puts "Updating engagement rates for all campaign metrics..."

    updated_count = 0
    total_count = CampaignMetric.count

    CampaignMetric.find_each(batch_size: 100) do |metric|
      # Calculate engagement rate using the model method
      calculated_rate = metric.engagement_rate

      # Update the database column
      metric.update_column(:engagement_rate, calculated_rate)
      updated_count += 1

      print "\rProcessed #{updated_count}/#{total_count} metrics..." if updated_count % 10 == 0
    end

    puts "\nCompleted! Updated engagement rates for #{updated_count} campaign metrics."
  end
end
