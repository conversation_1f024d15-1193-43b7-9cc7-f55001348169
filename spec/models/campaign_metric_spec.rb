# frozen_string_literal: true

require 'rails_helper'

RSpec.describe CampaignMetric, type: :model do
  let(:tenant) { create(:tenant) }
  let(:campaign) { create(:campaign, tenant: tenant) }
  let(:campaign_metric) { create(:campaign_metric, campaign: campaign) }

  describe 'associations' do
    it { should belong_to(:campaign) }
  end

  describe 'validations' do
    subject { campaign_metric }

    it { should validate_presence_of(:metric_date) }
    it { should validate_uniqueness_of(:metric_date).scoped_to(:campaign_id) }
    it { should validate_numericality_of(:impressions).is_greater_than_or_equal_to(0) }
    it { should validate_numericality_of(:clicks).is_greater_than_or_equal_to(0) }
    it { should validate_numericality_of(:conversions).is_greater_than_or_equal_to(0) }
    it { should validate_numericality_of(:revenue_cents).is_greater_than_or_equal_to(0) }
    it { should validate_numericality_of(:cost_cents).is_greater_than_or_equal_to(0) }
  end

  describe 'scopes' do
    let!(:recent_metric) { create(:campaign_metric, metric_date: 1.day.ago, campaign: campaign) }
    let!(:old_metric) { create(:campaign_metric, metric_date: 45.days.ago, campaign: campaign) }

    describe '.for_date_range' do
      it 'returns metrics within date range' do
        start_date = 2.days.ago.to_date
        end_date = Date.current

        metrics = CampaignMetric.for_date_range(start_date, end_date)
        expect(metrics).to include(recent_metric)
        expect(metrics).not_to include(old_metric)
      end
    end

    describe '.recent' do
      it 'returns metrics from last 30 days by default' do
        metrics = campaign.campaign_metrics.recent
        expect(metrics).to include(recent_metric)
        expect(metrics).not_to include(old_metric)
      end
    end

    describe '.this_month' do
      let!(:this_month_metric) { create(:campaign_metric, metric_date: Date.current.beginning_of_month, campaign: campaign) }

      it 'returns metrics from current month' do
        metrics = CampaignMetric.this_month
        expect(metrics).to include(this_month_metric)
      end
    end
  end

  describe 'customer lifecycle metrics' do
    describe '#customer_acquisition_cost' do
      it 'calculates CAC correctly' do
        metric = create(:campaign_metric, cost_cents: 10000, conversions: 5, campaign: campaign)
        expect(metric.customer_acquisition_cost).to eq(20.0)
      end

      it 'returns 0 when no conversions' do
        metric = create(:campaign_metric, cost_cents: 10000, conversions: 0, campaign: campaign)
        expect(metric.customer_acquisition_cost).to eq(0.0)
      end
    end

    describe '#customer_lifetime_value' do
      it 'calculates CLV with default retention and margin' do
        metric = create(:campaign_metric,
          revenue_cents: 10000,
          conversions: 2,
          campaign: campaign,
          custom_metrics: { 'retention_rate' => 0.85, 'profit_margin' => 0.30 }
        )

        # AOV = 50 (10000 cents / 2 conversions / 100), Retention = 0.85, Margin = 0.30
        # CLV = 50 * 0.30 * (0.85 / (1 - 0.85)) = 15 * (0.85 / 0.15) = 15 * 5.67 = 85
        expect(metric.customer_lifetime_value).to be_within(1.0).of(85.0)
      end

      it 'returns 0 when no conversions' do
        metric = create(:campaign_metric, conversions: 0, campaign: campaign)
        expect(metric.customer_lifetime_value).to eq(0.0)
      end
    end

    describe '#ltv_to_cac_ratio' do
      it 'calculates LTV:CAC ratio correctly' do
        metric = create(:campaign_metric,
          cost_cents: 5000,
          revenue_cents: 10000,
          conversions: 1,
          campaign: campaign,
          custom_metrics: { 'retention_rate' => 0.80, 'profit_margin' => 0.25 }
        )

        cac = 50.0 # 5000 cents / 1 conversion
        # CLV calculation: AOV=100, retention=0.8, margin=0.25
        # CLV = 100 * 0.25 * (0.8 / 0.2) = 25 * 4 = 100
        # Ratio = 100 / 50 = 2.0
        expect(metric.ltv_to_cac_ratio).to eq(2.0)
      end

      it 'returns 0 when CAC is 0' do
        metric = create(:campaign_metric, conversions: 0, campaign: campaign)
        expect(metric.ltv_to_cac_ratio).to eq(0.0)
      end
    end

    describe '#payback_period_days' do
      it 'calculates payback period correctly' do
        metric = create(:campaign_metric,
          cost_cents: 12000,
          revenue_cents: 12000,
          conversions: 1,
          campaign: campaign,
          custom_metrics: { 'retention_rate' => 0.85, 'profit_margin' => 0.30 }
        )

        # CAC = 120, CLV = 120 * 0.30 * (0.85/0.15) = 36 * 5.67 = 204
        # Monthly value = 204 / 12 = 17
        # Payback = 120 / 17 * 30 = ~212 days
        expect(metric.payback_period_days).to be > 100
      end
    end
  end

  describe 'content performance metrics' do
    describe '#content_engagement_score' do
      it 'calculates weighted engagement score' do
        metric = create(:campaign_metric,
          email_opens: 100,
          email_clicks: 20,
          social_engagements: 50,
          social_shares: 10,
          clicks: 100,
          conversions: 5,
          impressions: 1000,
          campaign: campaign,
          custom_metrics: {
            'email' => { 'total_sent' => 500 },
            'social' => { 'total_reach' => 2000 }
          }
        )

        # Email engagement = (100 + 20) / 500 * 100 = 24%
        # Social engagement = 50 / 2000 * 100 = 2.5%
        # Conversion rate = 5 / 100 * 100 = 5%
        # Score = (24 * 0.3 + 2.5 * 0.4 + 5 * 0.3) * 10 = (7.2 + 1.0 + 1.5) * 10 = 97
        expect(metric.content_engagement_score).to be_within(5.0).of(97.0)
      end
    end

    describe '#viral_coefficient' do
      it 'calculates viral coefficient correctly' do
        metric = create(:campaign_metric,
          social_engagements: 100,
          social_shares: 25,
          campaign: campaign
        )

        expect(metric.viral_coefficient).to eq(25.0)
      end

      it 'returns 0 when no engagements' do
        metric = create(:campaign_metric, social_engagements: 0, campaign: campaign)
        expect(metric.viral_coefficient).to eq(0.0)
      end
    end

    describe '#content_efficiency_score' do
      it 'calculates content efficiency correctly' do
        metric = create(:campaign_metric,
          email_opens: 100,
          email_clicks: 20,
          social_engagements: 50,
          conversions: 10,
          cost_cents: 5000,
          campaign: campaign
        )

        # Total engagements = 100 + 20 + 50 + 10 = 180
        # Efficiency = 180 / 50 = 3.6
        expect(metric.content_efficiency_score).to eq(3.6)
      end
    end
  end

  describe 'class methods' do
    let!(:metric1) { create(:campaign_metric, :high_performance, campaign: campaign) }
    let!(:metric2) { create(:campaign_metric, :low_performance, campaign: campaign, metric_date: 1.day.ago) }

    describe '.lifecycle_metrics_for_campaign' do
      it 'calculates aggregate lifecycle metrics' do
        result = CampaignMetric.lifecycle_metrics_for_campaign(campaign)

        expect(result).to include(:customer_acquisition_cost)
        expect(result).to include(:customer_lifetime_value)
        expect(result).to include(:ltv_to_cac_ratio)
        expect(result).to include(:total_customers_acquired)
        expect(result[:total_customers_acquired]).to eq(51) # 50 + 1 from traits
      end

      it 'returns default metrics for campaigns without data' do
        empty_campaign = create(:campaign, tenant: tenant)
        result = CampaignMetric.lifecycle_metrics_for_campaign(empty_campaign)

        expect(result[:customer_acquisition_cost]).to eq(0.0)
        expect(result[:customer_lifetime_value]).to eq(0.0)
        expect(result[:total_customers_acquired]).to eq(0)
      end
    end

    describe '.content_metrics_for_campaign' do
      it 'calculates aggregate content metrics' do
        result = CampaignMetric.content_metrics_for_campaign(campaign)

        expect(result).to include(:average_engagement_score)
        expect(result).to include(:total_viral_shares)
        expect(result).to include(:email_performance)
        expect(result).to include(:social_performance)
        expect(result[:total_viral_shares]).to eq(30) # 15 + 15 from factory defaults
      end

      it 'includes email and social performance breakdowns' do
        result = CampaignMetric.content_metrics_for_campaign(campaign)

        expect(result[:email_performance]).to include(:total_opens, :total_clicks)
        expect(result[:social_performance]).to include(:total_engagements, :total_shares, :total_comments)
      end
    end

    describe '.aggregate_for_campaign' do
      it 'aggregates basic metrics correctly' do
        result = CampaignMetric.aggregate_for_campaign(campaign)

        expect(result[:total_impressions]).to eq(5100) # 5000 + 100
        expect(result[:total_clicks]).to eq(505) # 500 + 5
        expect(result[:total_conversions]).to eq(51) # 50 + 1
        expect(result[:total_revenue]).to eq(505.0) # (50000 + 500) / 100
        expect(result[:total_cost]).to eq(110.0) # (10000 + 1000) / 100
      end
    end
  end

  describe 'helper methods' do
    describe '#email_engagement_rate' do
      it 'calculates email engagement rate' do
        metric = create(:campaign_metric,
          email_opens: 100,
          email_clicks: 20,
          campaign: campaign,
          custom_metrics: { 'email' => { 'total_sent' => 500 } }
        )

        expect(metric.email_engagement_rate).to eq(24.0) # (100 + 20) / 500 * 100
      end
    end

    describe '#social_engagement_rate' do
      it 'calculates social engagement rate' do
        metric = create(:campaign_metric,
          social_engagements: 50,
          campaign: campaign,
          custom_metrics: { 'social' => { 'total_reach' => 2000 } }
        )

        expect(metric.social_engagement_rate).to eq(2.5) # 50 / 2000 * 100
      end
    end
  end

  describe 'edge cases' do
    it 'handles zero values gracefully' do
      metric = create(:campaign_metric,
        impressions: 0,
        clicks: 0,
        conversions: 0,
        revenue_cents: 0,
        cost_cents: 0,
        campaign: campaign
      )

      expect(metric.click_through_rate).to eq(0.0)
      expect(metric.conversion_rate).to eq(0.0)
      expect(metric.customer_acquisition_cost).to eq(0.0)
      expect(metric.content_engagement_score).to be >= 0.0
    end

    it 'handles missing custom metrics gracefully' do
      metric = create(:campaign_metric,
        custom_metrics: {},
        campaign: campaign
      )

      expect(metric.email_total_sent).to eq(0)
      expect(metric.social_total_reach).to eq(0)
      expect(metric.customer_lifetime_value).to be >= 0.0
    end
  end
end
