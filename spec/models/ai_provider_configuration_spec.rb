require 'rails_helper'

RSpec.describe AiProviderConfiguration, type: :model do
  let(:tenant) { create(:tenant) }
  
  describe 'validations' do
    subject { build(:ai_provider_configuration, tenant: tenant) }

    it { should belong_to(:tenant) }
    it { should validate_presence_of(:provider_name) }
    it { should validate_inclusion_of(:provider_name).in_array(%w[openai anthropic gemini deepseek openrouter]) }
    it { should validate_presence_of(:ai_model_name) }
    it { should validate_length_of(:ai_model_name).is_at_least(1).is_at_most(100) }
    it { should validate_presence_of(:cost_per_token) }
    it { should validate_numericality_of(:cost_per_token).is_greater_than(0) }
    it { should validate_length_of(:default_for_task_type).is_at_most(50).allow_blank }

    context 'when default_for_task_type is present' do
      it 'validates uniqueness of default_for_task_type scoped to tenant' do
        create(:ai_provider_configuration, tenant: tenant, default_for_task_type: 'general')
        duplicate = build(:ai_provider_configuration, tenant: tenant, default_for_task_type: 'general')
        
        expect(duplicate).not_to be_valid
        expect(duplicate.errors[:default_for_task_type]).to include('has already been taken')
      end

      it 'allows same default_for_task_type for different tenants' do
        other_tenant = create(:tenant)
        create(:ai_provider_configuration, tenant: tenant, default_for_task_type: 'general')
        duplicate = build(:ai_provider_configuration, tenant: other_tenant, default_for_task_type: 'general')
        
        expect(duplicate).to be_valid
      end
    end
  end

  describe 'scopes' do
    let!(:active_config) { create(:ai_provider_configuration, tenant: tenant, is_active: true) }
    let!(:inactive_config) { create(:ai_provider_configuration, :inactive, tenant: tenant) }
    let!(:openai_config) { create(:ai_provider_configuration, :openai, tenant: tenant) }
    let!(:anthropic_config) { create(:ai_provider_configuration, :anthropic, tenant: tenant) }
    let!(:creative_config) { create(:ai_provider_configuration, :creative_content, tenant: tenant) }

    describe '.active' do
      it 'returns only active configurations' do
        expect(AiProviderConfiguration.active).to include(active_config)
        expect(AiProviderConfiguration.active).not_to include(inactive_config)
      end
    end

    describe '.for_provider' do
      it 'returns configurations for specific provider' do
        expect(AiProviderConfiguration.for_provider('openai')).to include(openai_config)
        expect(AiProviderConfiguration.for_provider('openai')).not_to include(anthropic_config)
      end
    end

    describe '.for_task_type' do
      it 'returns configurations for specific task type' do
        expect(AiProviderConfiguration.for_task_type('creative_content')).to include(creative_config)
      end
    end

    describe '.default_for' do
      it 'returns active configurations that are default for task type' do
        expect(AiProviderConfiguration.default_for('creative_content')).to include(creative_config)
        
        creative_config.update!(is_active: false)
        expect(AiProviderConfiguration.default_for('creative_content')).not_to include(creative_config)
      end
    end

    describe '.by_cost' do
      it 'orders configurations by cost per token' do
        cheap = create(:ai_provider_configuration, tenant: tenant, cost_per_token: 0.001)
        expensive = create(:ai_provider_configuration, tenant: tenant, cost_per_token: 0.01)
        
        expect(AiProviderConfiguration.by_cost.first).to eq(cheap)
        expect(AiProviderConfiguration.by_cost.last).to eq(expensive)
      end
    end
  end

  describe 'callbacks' do
    describe 'before_create :initialize_default_configuration' do
      it 'sets default configuration when none provided' do
        config = build(:ai_provider_configuration, tenant: tenant, configuration: nil)
        config.save!
        
        expect(config.configuration).to be_present
        expect(config.configuration['max_tokens']).to be_present
        expect(config.configuration['temperature']).to be_present
      end

      it 'does not override existing configuration' do
        custom_config = { 'max_tokens' => 1000, 'temperature' => 0.5 }
        config = build(:ai_provider_configuration, tenant: tenant, configuration: custom_config)
        config.save!
        
        expect(config.configuration['max_tokens']).to eq(1000)
        expect(config.configuration['temperature']).to eq(0.5)
      end
    end

    describe 'before_save :normalize_task_type' do
      it 'normalizes task type to lowercase and strips whitespace' do
        config = create(:ai_provider_configuration, tenant: tenant, default_for_task_type: '  CREATIVE_CONTENT  ')
        expect(config.default_for_task_type).to eq('creative_content')
      end
    end
  end

  describe 'configuration helpers' do
    let(:config) { create(:ai_provider_configuration, tenant: tenant) }

    describe '#configuration_value' do
      it 'returns value for existing key' do
        expect(config.configuration_value('max_tokens')).to eq(4096)
      end

      it 'returns default for non-existing key' do
        expect(config.configuration_value('non_existing', 'default')).to eq('default')
      end
    end

    describe '#update_configuration' do
      it 'updates configuration value' do
        config.update_configuration('max_tokens', 2048)
        expect(config.configuration_value('max_tokens')).to eq(2048)
      end
    end
  end

  describe 'capability helpers' do
    let(:config) { create(:ai_provider_configuration, :anthropic, tenant: tenant) }

    describe '#supports_capability?' do
      it 'returns true for supported capabilities' do
        expect(config.supports_capability?('text')).to be true
        expect(config.supports_capability?('vision')).to be true
      end

      it 'returns false for unsupported capabilities' do
        expect(config.supports_capability?('unsupported')).to be false
      end
    end

    describe '#supports_text?' do
      it 'returns true when text capability is supported' do
        expect(config.supports_text?).to be true
      end
    end

    describe '#supports_vision?' do
      it 'returns true when vision capability is supported' do
        expect(config.supports_vision?).to be true
      end
    end

    describe '#supports_function_calling?' do
      it 'returns true when function_calling capability is supported' do
        expect(config.supports_function_calling?).to be true
      end
    end
  end

  describe '#provider_available?' do
    let(:config) { create(:ai_provider_configuration, :openai, tenant: tenant) }

    context 'when API key is available in environment' do
      before { allow(ENV).to receive(:[]).with('OPENAI_API_KEY').and_return('test-key') }

      it 'returns true' do
        expect(config.provider_available?).to be true
      end
    end

    context 'when API key is available in credentials' do
      before do
        allow(ENV).to receive(:[]).with('OPENAI_API_KEY').and_return(nil)
        allow(Rails.application.credentials).to receive(:openai_api_key).and_return('test-key')
      end

      it 'returns true' do
        expect(config.provider_available?).to be true
      end
    end

    context 'when no API key is available' do
      before do
        allow(ENV).to receive(:[]).with('OPENAI_API_KEY').and_return(nil)
        allow(Rails.application.credentials).to receive(:openai_api_key).and_return(nil)
      end

      it 'returns false' do
        expect(config.provider_available?).to be false
      end
    end
  end

  describe '#estimate_cost' do
    let(:config) { create(:ai_provider_configuration, tenant: tenant, cost_per_token: 0.00001) }

    it 'calculates cost for input tokens only' do
      cost = config.estimate_cost(1000)
      expected_tokens = 1000 + (1000 * 0.3).to_i
      expect(cost).to eq((expected_tokens * 0.00001).round(6))
    end

    it 'calculates cost for input and output tokens' do
      cost = config.estimate_cost(1000, 500)
      expected_cost = (1500 * 0.00001).round(6)
      expect(cost).to eq(expected_cost)
    end
  end

  describe '#test_connection' do
    let(:config) { create(:ai_provider_configuration, :openai, tenant: tenant) }

    it 'returns success response for OpenAI' do
      result = config.test_connection
      expect(result[:success]).to be true
      expect(result[:response_time]).to be_present
    end

    it 'handles errors gracefully' do
      allow(config).to receive(:test_openai_connection).and_raise(StandardError, 'Connection failed')
      
      result = config.test_connection
      expect(result[:success]).to be false
      expect(result[:error]).to eq('Connection failed')
      expect(result[:response_time]).to be_present
    end
  end

  describe 'status helpers' do
    let(:active_config) { create(:ai_provider_configuration, tenant: tenant, is_active: true) }
    let(:inactive_config) { create(:ai_provider_configuration, :inactive, tenant: tenant) }

    describe '#active?' do
      it 'returns true for active configurations' do
        expect(active_config.active?).to be true
        expect(inactive_config.active?).to be false
      end
    end

    describe '#inactive?' do
      it 'returns true for inactive configurations' do
        expect(active_config.inactive?).to be false
        expect(inactive_config.inactive?).to be true
      end
    end

    describe '#default_for_task?' do
      let(:config) { create(:ai_provider_configuration, :creative_content, tenant: tenant) }

      it 'returns true when configuration is default for task type' do
        expect(config.default_for_task?('creative_content')).to be true
        expect(config.default_for_task?(:creative_content)).to be true
        expect(config.default_for_task?('general')).to be false
      end
    end
  end

  describe 'class methods' do
    describe '.find_best_for_task' do
      let!(:default_config) { create(:ai_provider_configuration, :creative_content, tenant: tenant) }
      let!(:fallback_config) { create(:ai_provider_configuration, :openai, tenant: tenant, cost_per_token: 0.001) }
      let!(:expensive_config) { create(:ai_provider_configuration, :anthropic, tenant: tenant, cost_per_token: 0.01) }

      before do
        # Mock provider availability
        allow_any_instance_of(AiProviderConfiguration).to receive(:provider_available?).and_return(true)
      end

      it 'returns default configuration when available' do
        result = AiProviderConfiguration.find_best_for_task('creative_content', tenant)
        expect(result).to eq(default_config)
      end

      it 'returns cheapest fallback when no default available' do
        result = AiProviderConfiguration.find_best_for_task('data_analysis', tenant)
        expect(result).to eq(fallback_config)
      end

      it 'returns nil when no configurations are available' do
        allow_any_instance_of(AiProviderConfiguration).to receive(:provider_available?).and_return(false)
        result = AiProviderConfiguration.find_best_for_task('creative_content', tenant)
        expect(result).to be_nil
      end
    end

    describe '.available_for_task' do
      let!(:active_config) { create(:ai_provider_configuration, tenant: tenant, is_active: true) }
      let!(:inactive_config) { create(:ai_provider_configuration, :inactive, tenant: tenant) }

      before do
        allow_any_instance_of(AiProviderConfiguration).to receive(:provider_available?).and_return(true)
      end

      it 'returns only active and available configurations' do
        result = AiProviderConfiguration.available_for_task('general', tenant)
        expect(result).to include(active_config)
        expect(result).not_to include(inactive_config)
      end
    end

    describe '.supported_task_types' do
      it 'returns array of supported task types' do
        task_types = AiProviderConfiguration.supported_task_types
        expect(task_types).to include('general', 'creative_content', 'data_analysis')
        expect(task_types).to be_an(Array)
      end
    end

    describe '.supported_providers' do
      it 'returns array of supported providers' do
        providers = AiProviderConfiguration.supported_providers
        expect(providers).to include('openai', 'anthropic', 'gemini')
        expect(providers).to be_an(Array)
      end
    end

    describe '.supported_capabilities' do
      it 'returns array of supported capabilities' do
        capabilities = AiProviderConfiguration.supported_capabilities
        expect(capabilities).to include('text', 'vision', 'function_calling')
        expect(capabilities).to be_an(Array)
      end
    end
  end
end
