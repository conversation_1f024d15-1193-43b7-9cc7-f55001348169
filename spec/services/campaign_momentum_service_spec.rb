# frozen_string_literal: true

require 'rails_helper'

RSpec.describe CampaignMomentumService, type: :service do
  let(:tenant) { create(:tenant) }
  let(:user) { create(:user, tenant: tenant) }
  let(:service) { described_class.new(tenant) }

  before do
    ActsAsTenant.current_tenant = tenant
  end

  describe '#real_time_momentum_data' do
    let!(:campaign) { create(:campaign, tenant: tenant, status: :active) }
    let!(:metric1) { create(:campaign_metric, campaign: campaign, metric_date: 3.days.ago) }
    let!(:metric2) { create(:campaign_metric, campaign: campaign, metric_date: 1.day.ago) }

    it 'returns complete momentum data structure' do
      result = service.real_time_momentum_data

      expect(result).to include(
        :trending_campaigns,
        :momentum_alerts,
        :velocity_metrics,
        :engagement_trends,
        :performance_forecasts
      )
    end

    it 'caches the result for 1 minute' do
      expect(Rails.cache).to receive(:fetch).with(
        "momentum_#{tenant.id}",
        expires_in: 1.minute
      ).and_call_original

      service.real_time_momentum_data
    end
  end

  describe '#trending_campaigns_data' do
    context 'with campaign data' do
      let!(:high_momentum_campaign) { create(:campaign, tenant: tenant, status: :active, name: 'High Momentum') }
      let!(:low_momentum_campaign) { create(:campaign, tenant: tenant, status: :active, name: 'Low Momentum') }

      let!(:high_metrics) do
        create(:campaign_metric, :high_performance,
               campaign: high_momentum_campaign,
               metric_date: 2.days.ago,
               impressions: 5000, clicks: 500, conversions: 50)
      end

      let!(:low_metrics) do
        create(:campaign_metric, :low_performance,
               campaign: low_momentum_campaign,
               metric_date: 2.days.ago,
               impressions: 1000, clicks: 10, conversions: 1)
      end

      it 'returns trending campaigns ordered by momentum score' do
        result = service.trending_campaigns_data

        expect(result).to be_an(Array)
        expect(result.length).to be <= 10
        expect(result.first).to include(
          :campaign_id,
          :campaign_name,
          :momentum_score,
          :trend_direction,
          :velocity,
          :recent_performance
        )
      end

      it 'calculates momentum scores correctly' do
        result = service.trending_campaigns_data
        high_momentum_data = result.find { |c| c[:campaign_name] == 'High Momentum' }
        low_momentum_data = result.find { |c| c[:campaign_name] == 'Low Momentum' }

        expect(high_momentum_data[:momentum_score]).to be >= low_momentum_data[:momentum_score]
      end

      it 'includes recent performance metrics' do
        result = service.trending_campaigns_data
        campaign_data = result.first

        expect(campaign_data[:recent_performance]).to include(
          :avg_ctr,
          :total_conversions,
          :total_revenue
        )
      end
    end

    context 'without campaign data' do
      it 'returns empty array' do
        result = service.trending_campaigns_data
        expect(result).to eq([])
      end
    end
  end

  describe '#momentum_alerts' do
    context 'with declining campaigns' do
      let!(:declining_campaign) { create(:campaign, tenant: tenant, status: :active, name: 'Declining Campaign') }
      let!(:good_metric) { create(:campaign_metric, :high_performance, campaign: declining_campaign, metric_date: 2.days.ago) }
      let!(:bad_metric) { create(:campaign_metric, :low_performance, campaign: declining_campaign, metric_date: 1.day.ago) }

      it 'detects declining performance alerts' do
        result = service.momentum_alerts

        declining_alerts = result.select { |a| a[:type] == 'declining_performance' }

        if declining_alerts.any?
          alert = declining_alerts.first
          expect(alert).to include(
            :type,
            :severity,
            :campaign_id,
            :campaign_name,
            :message,
            :timestamp
          )
          expect(alert[:severity]).to eq('warning')
        end
      end
    end

    context 'with breakout campaigns' do
      let!(:breakout_campaign) { create(:campaign, tenant: tenant, status: :active, name: 'Breakout Campaign') }

      before do
        # Create metrics showing strong growth
        create(:campaign_metric, :low_performance, campaign: breakout_campaign, metric_date: 7.days.ago)
        create(:campaign_metric, :high_performance, campaign: breakout_campaign, metric_date: 1.day.ago)
      end

      it 'detects breakout performance alerts' do
        result = service.momentum_alerts

        breakout_alerts = result.select { |a| a[:type] == 'breakout_performance' }

        if breakout_alerts.any?
          alert = breakout_alerts.first
          expect(alert[:severity]).to eq('info')
          expect(alert[:message]).to include('growth detected')
        end
      end
    end

    context 'with budget concerns' do
      let!(:budget_campaign) { create(:campaign, tenant: tenant, status: :active, budget_cents: 10000) }
      let!(:expensive_metric) { create(:campaign_metric, campaign: budget_campaign, cost_cents: 9500) } # 95% of budget

      it 'detects budget depletion alerts' do
        result = service.momentum_alerts

        budget_alerts = result.select { |a| a[:type] == 'budget_depletion' }
        expect(budget_alerts).not_to be_empty

        alert = budget_alerts.first
        expect(alert[:severity]).to eq('critical')
        expect(alert[:message]).to include('Budget')
        expect(alert[:message]).to include('depleted')
      end
    end

    it 'returns alerts sorted by timestamp (newest first)' do
      result = service.momentum_alerts

      if result.length > 1
        timestamps = result.map { |a| a[:timestamp] }
        expect(timestamps).to eq(timestamps.sort.reverse)
      end
    end

    it 'limits alerts to 20 items' do
      result = service.momentum_alerts
      expect(result.length).to be <= 20
    end
  end

  describe '#calculate_velocity_metrics' do
    context 'with multiple campaigns' do
      let!(:campaign1) { create(:campaign, tenant: tenant, status: :active, name: 'Campaign 1') }
      let!(:campaign2) { create(:campaign, tenant: tenant, status: :active, name: 'Campaign 2') }

      let!(:metric1_old) { create(:campaign_metric, campaign: campaign1, metric_date: 7.days.ago, conversions: 10) }
      let!(:metric1_new) { create(:campaign_metric, campaign: campaign1, metric_date: 1.day.ago, conversions: 20) }
      let!(:metric2_old) { create(:campaign_metric, campaign: campaign2, metric_date: 7.days.ago, conversions: 5) }
      let!(:metric2_new) { create(:campaign_metric, campaign: campaign2, metric_date: 1.day.ago, conversions: 8) }

      it 'calculates velocity metrics for all campaigns' do
        result = service.calculate_velocity_metrics

        expect(result).to include(
          :average_velocity,
          :campaign_velocities,
          :velocity_distribution
        )
      end

      it 'calculates average velocity correctly' do
        result = service.calculate_velocity_metrics
        expect(result[:average_velocity]).to be_a(Float)
        expect(result[:average_velocity]).to be >= 0
      end

      it 'returns campaign velocities sorted by velocity' do
        result = service.calculate_velocity_metrics
        velocities = result[:campaign_velocities]

        expect(velocities).to be_an(Array)
        expect(velocities.length).to eq(2)

        # Check sorting (highest velocity first)
        if velocities.length > 1
          expect(velocities.first[:velocity]).to be >= velocities.last[:velocity]
        end
      end

      it 'includes velocity distribution breakdown' do
        result = service.calculate_velocity_metrics
        distribution = result[:velocity_distribution]

        expect(distribution).to include(
          :high_velocity,
          :medium_velocity,
          :low_velocity,
          :declining
        )
      end
    end

    context 'without campaigns' do
      it 'returns default velocity metrics' do
        result = service.calculate_velocity_metrics

        expect(result[:average_velocity]).to eq(0.0)
        expect(result[:campaign_velocities]).to eq([])
        expect(result[:velocity_distribution]).to include(
          high_velocity: 0,
          medium_velocity: 0,
          low_velocity: 0,
          declining: 0
        )
      end
    end
  end

  describe '#recent_engagement_trends' do
    context 'with recent metrics' do
      let!(:campaign) { create(:campaign, tenant: tenant) }

      before do
        # Create metrics with different timestamps within last 24 hours
        [ 2.hours.ago, 1.hour.ago, Time.current ].each do |time|
          create(:campaign_metric,
                 campaign: campaign,
                 created_at: time,
                 email_opens: 50,
                 email_clicks: 10,
                 social_engagements: 25,
                 conversions: 5)
        end
      end

      it 'returns engagement trends structure' do
        result = service.recent_engagement_trends

        expect(result).to include(
          :hourly_engagement,
          :peak_hours,
          :engagement_velocity
        )
      end

      it 'includes hourly engagement data' do
        result = service.recent_engagement_trends
        hourly_data = result[:hourly_engagement]

        expect(hourly_data).to be_an(Array)
        if hourly_data.any?
          expect(hourly_data.first).to include(
            :hour,
            :total_opens,
            :total_clicks,
            :total_engagements,
            :total_conversions
          )
        end
      end

      it 'identifies peak engagement hours' do
        result = service.recent_engagement_trends
        peak_hours = result[:peak_hours]

        expect(peak_hours).to be_an(Array)
        expect(peak_hours.length).to be <= 3

        if peak_hours.any?
          expect(peak_hours.first).to include(:hour, :engagement_count)
        end
      end

      it 'calculates engagement velocity' do
        result = service.recent_engagement_trends
        expect(result[:engagement_velocity]).to be_a(Float)
      end
    end

    context 'without recent metrics' do
      it 'returns empty trends data' do
        result = service.recent_engagement_trends

        expect(result[:hourly_engagement]).to eq([])
        expect(result[:peak_hours]).to eq([])
        expect(result[:engagement_velocity]).to eq(0.0)
      end
    end
  end

  describe '#generate_performance_forecasts' do
    context 'with sufficient historical data' do
      let!(:campaign) { create(:campaign, tenant: tenant, status: :active, name: 'Forecast Campaign') }

      before do
        # Create 5 days of historical data for forecasting
        5.times do |i|
          create(:campaign_metric,
                 campaign: campaign,
                 metric_date: (i + 1).days.ago,
                 conversions: 10 + (i * 2),
                 revenue_cents: (1000 + (i * 200)),
                 impressions: 1000,
                 clicks: 100)
        end
      end

      it 'generates forecasts for active campaigns' do
        result = service.generate_performance_forecasts

        expect(result).to be_an(Array)

        if result.any?
          forecast = result.first
          expect(forecast).to include(
            :campaign_id,
            :campaign_name,
            :forecast_period,
            :predicted_metrics,
            :confidence_level
          )

          expect(forecast[:forecast_period]).to eq('7_days')
          expect(forecast[:predicted_metrics]).to include(
            :predicted_conversions,
            :predicted_revenue,
            :predicted_ctr
          )
        end
      end

      it 'calculates confidence levels' do
        result = service.generate_performance_forecasts

        if result.any?
          forecast = result.first
          expect(forecast[:confidence_level]).to be_a(Float)
          expect(forecast[:confidence_level]).to be_between(0, 100)
        end
      end
    end

    context 'without sufficient historical data' do
      let!(:new_campaign) { create(:campaign, tenant: tenant, status: :active) }
      let!(:single_metric) { create(:campaign_metric, campaign: new_campaign) }

      it 'excludes campaigns without sufficient data' do
        result = service.generate_performance_forecasts
        campaign_ids = result.map { |f| f[:campaign_id] }

        expect(campaign_ids).not_to include(new_campaign.id)
      end
    end
  end

  describe 'helper methods' do
    let!(:campaign) { create(:campaign, tenant: tenant) }

    describe '#calculate_momentum_score' do
      context 'with sufficient metrics' do
        before do
          create(:campaign_metric, campaign: campaign, metric_date: 7.days.ago, conversions: 10)
          create(:campaign_metric, campaign: campaign, metric_date: 1.day.ago, conversions: 20)
        end

        it 'calculates momentum score between 0 and 100' do
          score = service.send(:calculate_momentum_score, campaign)
          expect(score).to be_between(0, 100)
          expect(score).to be_a(Float)
        end
      end

      context 'with insufficient metrics' do
        it 'returns 0 for campaigns without enough data' do
          score = service.send(:calculate_momentum_score, campaign)
          expect(score).to eq(0.0)
        end
      end
    end

    describe '#determine_trend_direction' do
      it 'determines growing trend' do
        create(:campaign_metric, :low_performance, campaign: campaign, metric_date: 3.days.ago)
        create(:campaign_metric, :high_performance, campaign: campaign, metric_date: 1.day.ago)

        trend = service.send(:determine_trend_direction, campaign)
        expect(trend).to be_in([ 'growing', 'stable', 'declining' ])
      end

      it 'returns stable for insufficient data' do
        trend = service.send(:determine_trend_direction, campaign)
        expect(trend).to eq('stable')
      end
    end

    describe '#calculate_campaign_velocity' do
      it 'calculates velocity correctly' do
        create(:campaign_metric, campaign: campaign, metric_date: 7.days.ago, conversions: 10, revenue_cents: 1000)
        create(:campaign_metric, campaign: campaign, metric_date: 1.day.ago, conversions: 20, revenue_cents: 2000)

        velocity = service.send(:calculate_campaign_velocity, campaign)
        expect(velocity).to be_a(Float)
      end

      it 'returns 0 for insufficient data' do
        velocity = service.send(:calculate_campaign_velocity, campaign)
        expect(velocity).to eq(0.0)
      end
    end
  end

  describe 'error handling' do
    it 'handles missing campaigns gracefully' do
      result = service.real_time_momentum_data

      expect(result[:trending_campaigns]).to eq([])
      expect(result[:momentum_alerts]).to be_an(Array)
      expect(result[:velocity_metrics][:average_velocity]).to eq(0.0)
    end

    it 'handles database errors gracefully' do
      allow_any_instance_of(described_class).to receive(:detect_declining_campaigns).and_raise(ActiveRecord::StatementInvalid.new('Database error'))

      result = service.momentum_alerts
      expect(result).to be_an(Array)
    end
  end
end
