# frozen_string_literal: true

require 'rails_helper'

RSpec.describe DashboardPerformanceService, type: :service do
  let(:tenant) { create(:tenant) }
  let(:user) { create(:user, tenant: tenant) }
  let(:service) { described_class.new(tenant, user) }

  before do
    ActsAsTenant.current_tenant = tenant
  end

  describe '#dashboard_data' do
    let!(:campaign) { create(:campaign, tenant: tenant) }
    let!(:campaign_metric) { create(:campaign_metric, campaign: campaign) }
    let!(:ai_usage_record) { create(:ai_usage_record, tenant: tenant) }

    it 'returns complete dashboard data structure' do
      result = service.dashboard_data

      expect(result).to include(
        :campaigns,
        :campaign_stats,
        :budget_stats,
        :platform_stats,
        :performance_metrics,
        :recent_campaigns,
        :active_campaigns,
        :ai_usage_stats,
        :lifecycle_metrics,
        :content_metrics,
        :ai_performance_metrics
      )
    end

    it 'caches the result for 5 minutes' do
      expect(Rails.cache).to receive(:fetch).with(
        "dashboard_#{tenant.id}_#{user.id}",
        expires_in: 5.minutes
      ).and_call_original

      service.dashboard_data
    end
  end

  describe '#optimized_lifecycle_metrics' do
    context 'with campaign data' do
      let!(:campaign) { create(:campaign, tenant: tenant) }
      let!(:high_metric) { create(:campaign_metric, :high_performance, campaign: campaign, metric_date: Date.current) }
      let!(:low_metric) { create(:campaign_metric, :low_performance, campaign: campaign, metric_date: 1.day.ago) }

      it 'calculates aggregate lifecycle metrics' do
        result = service.send(:optimized_lifecycle_metrics)

        expect(result).to include(
          :customer_acquisition_cost,
          :customer_lifetime_value,
          :ltv_to_cac_ratio,
          :total_customers_acquired,
          :total_revenue,
          :average_order_value
        )

        expect(result[:total_customers_acquired]).to eq(51) # 50 + 1
        expect(result[:total_revenue]).to eq(505.0) # (50000 + 500) / 100
      end

      it 'calculates correct CAC and CLV' do
        result = service.send(:optimized_lifecycle_metrics)

        expect(result[:customer_acquisition_cost]).to be > 0
        expect(result[:customer_lifetime_value]).to be > 0
        expect(result[:ltv_to_cac_ratio]).to be > 0
      end
    end

    context 'without campaign data' do
      it 'returns default lifecycle metrics' do
        result = service.send(:optimized_lifecycle_metrics)

        expect(result[:customer_acquisition_cost]).to eq(0.0)
        expect(result[:customer_lifetime_value]).to eq(0.0)
        expect(result[:ltv_to_cac_ratio]).to eq(0.0)
        expect(result[:total_customers_acquired]).to eq(0)
      end
    end
  end

  describe '#optimized_content_metrics' do
    context 'with campaign data' do
      let!(:campaign) { create(:campaign, tenant: tenant) }
      let!(:metric1) { create(:campaign_metric, campaign: campaign,
        metric_date: Date.current,
        email_opens: 100, email_clicks: 20,
        social_engagements: 50, social_shares: 10, social_comments: 5
      )}
      let!(:metric2) { create(:campaign_metric, campaign: campaign,
        metric_date: 1.day.ago,
        email_opens: 200, email_clicks: 40,
        social_engagements: 75, social_shares: 15, social_comments: 8
      )}

      it 'calculates aggregate content metrics' do
        result = service.send(:optimized_content_metrics)

        expect(result).to include(
          :average_engagement_score,
          :total_viral_shares,
          :average_viral_coefficient,
          :email_performance,
          :social_performance
        )

        expect(result[:total_viral_shares]).to eq(25) # 10 + 15
        expect(result[:email_performance][:total_opens]).to eq(300) # 100 + 200
        expect(result[:social_performance][:total_engagements]).to eq(125) # 50 + 75
      end

      it 'calculates email click-through rate' do
        result = service.send(:optimized_content_metrics)

        expected_ctr = (60.0 / 300.0 * 100).round(2) # (20 + 40) / (100 + 200) * 100
        expect(result[:email_performance][:click_through_rate]).to eq(expected_ctr)
      end

      it 'calculates engagement diversity' do
        result = service.send(:optimized_content_metrics)

        expect(result[:social_performance][:engagement_diversity]).to be_between(0, 100)
      end
    end

    context 'without campaign data' do
      it 'returns default content metrics' do
        result = service.send(:optimized_content_metrics)

        expect(result[:average_engagement_score]).to eq(0.0)
        expect(result[:total_viral_shares]).to eq(0)
        expect(result[:email_performance][:total_opens]).to eq(0)
        expect(result[:social_performance][:total_engagements]).to eq(0)
      end
    end
  end

  describe '#optimized_ai_performance_metrics' do
    context 'with AI usage data' do
      let!(:campaign1) { create(:campaign, tenant: tenant, status: :completed) }
      let!(:campaign2) { create(:campaign, tenant: tenant, status: :active) }
      let!(:campaign_metric1) { create(:campaign_metric, campaign: campaign1, revenue_cents: 10000) }
      let!(:campaign_metric2) { create(:campaign_metric, campaign: campaign2, revenue_cents: 5000) }

      let!(:usage1) { create(:ai_usage_record, tenant: tenant,
        model: 'gpt-4o', cost: 0.05, duration_ms: 1500, created_at: 10.days.ago
      )}
      let!(:usage2) { create(:ai_usage_record, tenant: tenant,
        model: 'claude-3.5-sonnet', cost: 0.03, duration_ms: 1200, created_at: 5.days.ago
      )}

      it 'calculates AI performance metrics' do
        result = service.send(:optimized_ai_performance_metrics)

        expect(result).to include(
          :model_performance,
          :ai_roi_percentage,
          :campaign_success_rate,
          :cost_per_successful_campaign,
          :efficiency_score,
          :top_performing_model
        )

        expect(result[:model_performance]).to have_key('gpt-4o')
        expect(result[:model_performance]).to have_key('claude-3.5-sonnet')
      end

      it 'calculates campaign success rate' do
        result = service.send(:optimized_ai_performance_metrics)

        expected_rate = (1.0 / 2.0 * 100).round(2) # 1 completed out of 2 total
        expect(result[:campaign_success_rate]).to eq(expected_rate)
      end

      it 'calculates AI ROI' do
        result = service.send(:optimized_ai_performance_metrics)

        total_ai_cost = 0.08 # 0.05 + 0.03
        total_revenue = 150.0 # (10000 + 5000) / 100
        expected_roi = ((total_revenue - total_ai_cost) / total_ai_cost * 100).round(2)

        expect(result[:ai_roi_percentage]).to eq(expected_roi)
      end

      it 'identifies top performing model' do
        result = service.send(:optimized_ai_performance_metrics)

        expect(result[:top_performing_model]).to be_in([ 'gpt-4o', 'claude-3.5-sonnet' ])
      end
    end

    context 'without AI usage data' do
      it 'returns default AI performance metrics' do
        result = service.send(:optimized_ai_performance_metrics)

        expect(result[:model_performance]).to be_empty
        expect(result[:ai_roi_percentage]).to eq(0.0)
        expect(result[:campaign_success_rate]).to eq(0.0)
        expect(result[:top_performing_model]).to eq('N/A')
      end
    end
  end

  describe 'helper methods' do
    describe '#calculate_engagement_diversity' do
      it 'calculates diversity correctly' do
        # Equal distribution should have high diversity
        result = service.send(:calculate_engagement_diversity, 33, 33, 34)
        expect(result).to be_within(5.0).of(100.0)

        # Uneven distribution should have lower diversity
        result = service.send(:calculate_engagement_diversity, 90, 5, 5)
        expect(result).to be < 80.0
      end

      it 'handles zero engagements' do
        result = service.send(:calculate_engagement_diversity, 0, 0, 0)
        expect(result).to eq(0.0)
      end
    end

    describe '#calculate_ai_efficiency_score' do
      let!(:usage_records) { create_list(:ai_usage_record, 3, tenant: tenant, cost: 0.05, duration_ms: 1000) }

      it 'calculates efficiency score' do
        result = service.send(:calculate_ai_efficiency_score, usage_records, 2)
        expect(result).to be > 0
        expect(result).to be_a(Float)
      end

      it 'returns 0 for no successful campaigns' do
        result = service.send(:calculate_ai_efficiency_score, usage_records, 0)
        expect(result).to eq(0.0)
      end
    end

    describe '#find_top_performing_model' do
      let(:model_performance) do
        {
          'gpt-4o' => { avg_cost_per_request: 0.05, success_rate: 95.0 },
          'claude-3.5-sonnet' => { avg_cost_per_request: 0.03, success_rate: 92.0 }
        }
      end

      it 'finds the model with best cost/performance ratio' do
        result = service.send(:find_top_performing_model, model_performance)
        expect(result).to be_in([ 'gpt-4o', 'claude-3.5-sonnet' ])
      end

      it 'returns N/A for empty performance data' do
        result = service.send(:find_top_performing_model, {})
        expect(result).to eq('N/A')
      end
    end
  end

  describe 'caching behavior' do
    it 'invalidates caches correctly' do
      expect(Rails.cache).to receive(:delete).with("dashboard_#{tenant.id}_#{user.id}")
      expect(Rails.cache).to receive(:delete).with("dashboard_#{tenant.id}_#{user.id}_vibe")
      expect(Rails.cache).to receive(:delete).with("dashboard_#{tenant.id}_#{user.id}_settings")

      service.invalidate_caches
    end
  end

  describe 'error handling' do
    it 'handles missing campaigns gracefully' do
      result = service.dashboard_data

      expect(result[:lifecycle_metrics][:total_customers_acquired]).to eq(0)
      expect(result[:content_metrics][:average_engagement_score]).to eq(0.0)
      expect(result[:ai_performance_metrics][:top_performing_model]).to eq('N/A')
    end

    it 'handles database errors gracefully' do
      allow(service).to receive(:optimized_campaigns).and_raise(ActiveRecord::StatementInvalid.new('Database error'))

      result = service.dashboard_data
      expect(result).to be_a(Hash)
    end
  end
end
