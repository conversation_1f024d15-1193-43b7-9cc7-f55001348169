# frozen_string_literal: true

FactoryBot.define do
  factory :ai_usage_record do
    association :tenant
    model { 'gpt-4o' }
    task_type { 'creative_content' }
    input_tokens { 100 }
    output_tokens { 50 }
    total_tokens { 150 }
    duration_ms { 1500.0 }
    cost { 0.005 }
    request_timestamp { Time.current }
    metadata { { 'campaign_id' => 1, 'user_id' => 1 } }

    trait :expensive do
      cost { 0.50 }
      input_tokens { 1000 }
      output_tokens { 500 }
      total_tokens { 1500 }
    end

    trait :fast do
      duration_ms { 500.0 }
    end

    trait :slow do
      duration_ms { 5000.0 }
    end

    trait :claude do
      model { 'claude-3.5-sonnet' }
      cost { 0.003 }
    end

    trait :recent do
      request_timestamp { 1.hour.ago }
    end

    trait :old do
      request_timestamp { 45.days.ago }
    end
  end
end
