# frozen_string_literal: true

##
# Performance Optimization Configuration
#
# Sets up performance monitoring, caching, and optimization features
# for the AI Marketing Hub application.
#

Rails.application.configure do
  # Add performance monitoring middleware (temporarily disabled for startup)
  # config.middleware.use PerformanceMonitoringMiddleware

  # Configure query optimization
  config.after_initialize do
    # Set up query logging for development
    if Rails.env.development?
      ActiveRecord::Base.logger = Logger.new(STDOUT)
      ActiveRecord::Base.logger.level = Logger::DEBUG
    end

    # Configure connection pool for better performance
    ActiveRecord::Base.connection_pool.instance_eval do
      @size = Rails.env.production? ? 25 : 5
      @checkout_timeout = 10
    end

    # Set up cache warming for production
    if Rails.env.production?
      # Schedule cache warming every hour
      Rails.application.config.after_initialize do
        # This would typically be done with a cron job or scheduler
        # CacheWarmupJob.set(wait: 1.minute).perform_later(Tenant.first.id) if Tenant.exists?
      end
    end
  end

  # Configure caching
  if Rails.env.production?
    # Use Redis for caching in production
    config.cache_store = :redis_cache_store, {
      url: ENV["REDIS_URL"] || "redis://localhost:6379/1",
      expires_in: 1.hour,
      namespace: "ai_marketing_hub",
      pool_size: 5,
      pool_timeout: 5,
      reconnect_attempts: 3
    }
  else
    # Use memory store for development/test
    config.cache_store = :memory_store, {
      size: 64.megabytes,
      expires_in: 30.minutes
    }
  end

  # Configure Active Job for background processing
  if Rails.env.production?
    config.active_job.queue_adapter = :solid_queue
  else
    config.active_job.queue_adapter = :async
  end

  # Performance-related Active Record configurations
  config.active_record.query_log_tags_enabled = true
  config.active_record.query_log_tags = [
    :application,
    :controller,
    :action,
    {
      request_id: ->(context) { context[:controller]&.request&.request_id },
      tenant_id: ->(context) { ActsAsTenant.current_tenant&.id }
    }
  ]

  # Configure database query timeout
  config.active_record.query_timeout = 30.seconds if Rails.env.production?

  # Set up performance monitoring alerts (in production)
  if Rails.env.production?
    config.after_initialize do
      # Set up periodic performance checks
      Rails.application.config.performance_check_interval = 5.minutes

      # This would typically integrate with your monitoring service
      # PerformanceAlertJob.set(wait: 5.minutes).perform_later
    end
  end
end

##
# Performance Optimization Helpers
#
module PerformanceOptimization
  ##
  # Benchmark a block of code and log the results
  #
  # @param description [String] Description of the operation
  # @param logger [Logger] Logger to use (defaults to Rails.logger)
  # @yield Block to benchmark
  # @return [Object] Result of the yielded block
  #
  def self.benchmark(description, logger: Rails.logger)
    start_time = Time.current
    result = yield
    end_time = Time.current

    duration = ((end_time - start_time) * 1000).round(2)
    logger.info "[BENCHMARK] #{description}: #{duration}ms"

    result
  end

  ##
  # Execute a block with query counting
  #
  # @param description [String] Description of the operation
  # @yield Block to execute
  # @return [Hash] Result with query count and execution result
  #
  def self.with_query_counting(description)
    query_count_before = query_count
    start_time = Time.current

    result = yield

    end_time = Time.current
    query_count_after = query_count

    {
      result: result,
      query_count: query_count_after - query_count_before,
      duration_ms: ((end_time - start_time) * 1000).round(2),
      description: description
    }
  end

  ##
  # Get current query count
  #
  # @return [Integer] Current query count
  #
  def self.query_count
    if defined?(ActiveRecord::QueryLogs) && ActiveRecord::QueryLogs.respond_to?(:query_count)
      ActiveRecord::QueryLogs.query_count
    else
      0
    end
  end

  ##
  # Cache a block result with automatic key generation
  #
  # @param key_parts [Array] Parts to build cache key from
  # @param expires_in [ActiveSupport::Duration] Cache expiration time
  # @yield Block to cache
  # @return [Object] Cached or computed result
  #
  def self.cache_result(key_parts, expires_in: 15.minutes)
    cache_key = "perf_cache:#{key_parts.join(':')}"

    Rails.cache.fetch(cache_key, expires_in: expires_in) do
      yield
    end
  end

  ##
  # Warm up caches for all tenants
  #
  def self.warm_all_caches
    Tenant.find_each do |tenant|
      CacheWarmupJob.perform_later(tenant.id)
    end
  end

  ##
  # Get performance statistics
  #
  # @return [Hash] Performance statistics
  #
  def self.performance_stats
    {
      cache_stats: cache_statistics,
      database_stats: database_statistics,
      memory_stats: memory_statistics,
      recent_performance: PerformanceMonitoringMiddleware.performance_summary
    }
  end

  ##
  # Get cache statistics
  #
  # @return [Hash] Cache statistics
  #
  def self.cache_statistics
    if Rails.cache.respond_to?(:stats)
      Rails.cache.stats
    else
      {
        hits: 0,
        misses: 0,
        hit_rate: 0.0,
        size: 0
      }
    end
  end

  ##
  # Get database statistics
  #
  # @return [Hash] Database statistics
  #
  def self.database_statistics
    pool = ActiveRecord::Base.connection_pool

    {
      pool_size: pool.size,
      active_connections: pool.connections.count,
      available_connections: pool.available_connection_count,
      checked_out_connections: pool.checked_out_connection_count
    }
  end

  ##
  # Get memory statistics
  #
  # @return [Hash] Memory statistics
  #
  def self.memory_statistics
    if defined?(GC.stat)
      gc_stats = GC.stat
      {
        heap_live_slots: gc_stats[:heap_live_slots],
        heap_free_slots: gc_stats[:heap_free_slots],
        total_allocated_objects: gc_stats[:total_allocated_objects],
        gc_count: gc_stats[:count],
        memory_usage_mb: (gc_stats[:heap_live_slots] * 40 / 1024.0 / 1024.0).round(2)
      }
    else
      {
        memory_usage_mb: 0.0,
        gc_count: 0
      }
    end
  end
end

# Add performance helpers to ApplicationController
if defined?(ApplicationController)
  ApplicationController.class_eval do
    include PerformanceOptimization

    # Add benchmark method to controllers
    def benchmark(description, &block)
      PerformanceOptimization.benchmark(description, logger: logger, &block)
    end

    # Add query counting to controllers
    def with_query_counting(description, &block)
      PerformanceOptimization.with_query_counting(description, &block)
    end
  end
end
